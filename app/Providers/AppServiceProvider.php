<?php

namespace App\Providers;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // URL::forceScheme('https');

        // get fixed number
        View::composer('company.*', function ($view) {
            $view->with('fixed3digit', 0.001);
            $view->with('fixed5digit', 0.00001);
            $view->with('roundFixed3digit', 3);
            $view->with('companyFixedDigitNumber', getFixDigitFrontValue());
        });
    }
}
