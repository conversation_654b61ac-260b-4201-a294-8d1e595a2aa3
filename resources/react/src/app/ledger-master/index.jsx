import React, { useContext, useEffect, useRef, useState } from "react";
import Helmet from "react-helmet";
import { Col, Form, Row, Container, OverlayTrigger, Tooltip, Modal } from "react-bootstrap";
import PhoneInput from "react-phone-input-2";
import { useDispatch, useSelector } from "react-redux";
import { StateContext } from "../../context/StateContext";
import CountrySelect from "../../shared/CountrySelect";
import * as Yup from "yup";
import { FormInput } from "../../components/ui/Input";
import { getEditData, handleSubmitLedger } from "./SubmitLedger";
import useDropdownOption from "../../shared/dropdownList";
import ReactSelect from "../../components/ui/ReactSelect";
import AddItemCategoryModal from "../modal/Item/ItemCategoryModal";
import { useFormik } from "formik";
import AddTransportModal from "../modal/Transport/TransportModal";
import DocumentModal from "../common/DocumentModal";
import {
    addMasterLedger,
    checkCapitalLedgerRatio,
    deleteItemStock,
    fetchBankDetail,
    fetchItemStockList,
    fetchLedgerGroupList,
    fetchTcsRateDetail,
    fetchTdsRateDetail,
    getLedgerGroupDetail,
    getLedgerModelDetail,
    ifscDetail,
    ledgerGroupDetail,
    updateMasterLedger,
} from "../../store/ledger/ledgerSlice";
import {
    checkGstNumberAlreadyUsed,
    fetchEntityData,
    fetchGstData,
    fetchShippingGstData,
    gstDetail,
    shippingGst,
} from "../../store/gst/gstSlice";
import { convertNameIntoCamelCase, fetchGstDetail } from "../../shared/prepareData";
import { CheckGstValidate, CheckPanValidate, formattedDate } from "../../shared/calculation";
import { fetchTransportById, fetchTransportList } from "../../store/transport/transportSlice";
import AddBrokerModal from "../modal/Broker/BrokerModal";
import LocationOfAsset from "../modal/Ledger/locationOfAsset";
import {
    fetchBrokerDetailsById,
    fetchBrokerList,
    getBrokerModelDetail,
} from "../../store/broker/brokerSlice";
import { fetchShippingAddressList } from "../../store/shippingAddress/shippingAddressSlice";
import WarningModal from "../common/WarningModal";
import { LedgerType, toastType } from "../../constants";
import { renderLedgerDetail } from "./SubLedgerDetail";
import Toast from "../../components/ui/Toast";
import { useParams, useSearchParams } from "react-router-dom";
import ShippingFromModal from "../modal/PartyAddress/ShippingFromModal";
import { fetchCompanyDetails } from "../../store/company/companySlice";
import { addToast, errorToast } from "../../store/actions/toastAction";
import { fetchGstRateList } from "../../store/rate/rateSlice";
import Error404Page from "../common/404";
import AddItemStockModal from "./openingStockModal";
import moment from "moment";
import Loader from "../../shared/loader";
import deleteSvg from "../../assets/images/delete.svg";
import BillWiseModal from "../modal/Ledger/BillWiseModal";
import Close from "../../assets/images/svg/close";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import CustomHelmet from "../../shared/helmet";
import Datepicker from "../../components/ui/DatePicker";
import CessRateModal from "../modal/Item/CessRateModal";

const LedgerTransaction = ({ fetchLedgerDetail, action }) => {
    const { id } = useParams();
    const formRef = useRef(null);
    const [searchParams] = useSearchParams();
    const { gstQuote, loader, setLoader, isDisable, setIsDisable } = useContext(StateContext);
    const {shippingAddresses} = useSelector(selector => selector);
    const [isDefault, setIsDefault] = useState(false);
    const [totalAmount, setTotalAmount] = useState(0);
    const [receivedAmount, setReceivedAmount] = useState(0);
    const [pendingAmount, setPendingAmount] = useState(0);
    const [DrCrAmount, setDrCrAmount] = useState(0);
    const [isShowDrCr, setIsShowDrCr] = useState("")
    const [swiftCodeShow, setSwiftCodeShow] = useState(false);
    const [swiftCodeLabel, setSwiftCodeLabel] = useState("");
    const [isAddCessRate, setIsAddCessRate] = useState(false);
    const [cessRateId, setCessRateId] = useState("");

    //shortcut-keys
    useTransactionShortcuts(formRef)

    const initialValue = {
        id: "",
        name: "",
        group_id: "",
        is_use_in_other_companies: 0,
        parent_id: "",
        new_group_name: "",
        party_details: {
            gstin: "",
            billing_address: {
                address_1: "",
                address_2: "",
                country_id: 101,
                state_id: "",
                city_id: "",
                pin_code: "",
            },
            same_as_billing_address: true,
            shipping_address: {
                address_name:"",
                party_name_same_as_address_name: true,
                shipping_gstin: null,
                shipping_name: null,
                address_1: "",
                address_2: "",
                country_id: 101,
                state_id: "",
                city_id: "",
                pin_code: "",
            },
            same_as_shipping_address: {
                shipping_gstin: null,
                shipping_name: null,
                address_1: "",
                address_2: "",
                country_id: 101,
                state_id: "",
                city_id: "",
                pin_code: "",
            },
            contact_person_name: "",
            region_iso_1: "in",
            region_code_1: "+91",
            contact_person_phone_1: "",
            contact_person_phone_input_1: "",
            region_iso_2: "in",
            region_code_2: "+91",
            contact_person_phone_2: "",
            contact_person_phone_input_2: "",
            contact_person_email: "",
            contact_person_website: "",
        },
        tax_details: {
            pan: "",
            type_of_entity: "",
            gst_registration_type: "",
            gst_return_status: null,
            is_tds_applicable: false,
            tan: "",
            type_of_entity_character: "",
            cin_number: "",
        },
        other_details: {
            credit_period: "",
            credit_period_type: "",
            allow_credit_limit: 0,
            credit_limit: "",
            credit_limit_action: "",
            broker: "",
            brokerage_percentage: "",
            brokerage_on_value: "",
            transporter: "",
            upload_document: null,
        },
        location_of_asset_id: "",
        //income
        income_type: "",
        is_gst_applicable: 0,
        gst_tax_id: "",
        gst_cess_rate: "",
        hsn_sac_code: "",
        description: "",
        //expense
        expense_type: "",
        //bank
        account_holder_name: "",
        account_number: "",
        account_type: 0,
        ifsc_code: "",
        bank_name: "",
        bank_branch: "",
        upi_id: "",
        swift_code: null,
        swift_code_label: "",
        // secure loan
        secured_loan_type: "",
        loan_account_number: "",
        name_of_financier: "",
        rate_of_annum: "",

        //unsecure
        unsecured_loan_type: "",
        pan: "",

        //Taxes
        tax_type: "",

        individual_huf: "",
        for_other: "",
        pan_not_given: "",
        calculated_on: 2,
        individual_bill_wise: "",
        yearly_total: "",

        // capital
        name_of_proprietor: "",
        holding_ratio: "",
        profit_loss_sharing_ratio: "",
        pan_number: "",
        aadhar_number: "",
        address_1: "",
        address_2: "",
        pin_code: "",
        country_id: 101,
        state_id: companyState,
        city_id: "",

        //loan
        address: {
            address_1: "",
            address_2: "",
            country_id: 101,
            state_id: companyState,
            city_id: "",
            pin_code: "",
        },
        contact_details: {
            contact_person_name: "",
            region_iso_1: "in",
            region_code_1: "+91",
            contact_person_phone_1: "",
            contact_person_phone_input_1: "",
            region_iso_2: "in",
            region_code_2: "+91",
            contact_person_phone_2: "",
            contact_person_phone_input_2: "",
            contact_person_email: "",
            upload_document: null,
            contact_person_website: "",
        },
        opening_balance_details: {
            opening_balance: "",
            opening_balance_dr_cr: null,
            bill_wise: ""
        },
        action: action,
        parent_action: action,
        submitType: "",
        rounding_method: 1,
        bill_wise_opening_balance_details:Array(5).fill({
            voucher_number: "",
            voucher_date: "",
            due_date: "",
            transaction_type: null,
            total_amount: "",
            received_amount: "",
            paid_amount: "",
            pending_amount: "",
            is_editable: true
        })
    };

    const schema = Yup.object({
        tax_details: Yup.object().shape({
            pan: Yup.string()
                .nullable()
                .test(
                    "is-valid-gst",
                    "PAN Number is Invalid",
                    value => !value || CheckPanValidate(value)
                ),
        }),
    });

    const formik = useFormik({
        initialValues: initialValue,
        validationSchema: schema,
        onSubmit: values => {
            if (values?.parent_action === "Supplier" || values?.parent_action === "Customers") {
                const email = values.party_details.contact_person_email;
                if (email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)) {
                    dispatch(
                        errorToast({
                            text: "Enter a valid email address",
                            type: toastType.ERROR,
                        })
                    );
                    return;
                }
            }
            handleSubmit(values);
        },
    });

    const swiftCodelabels = (data) => {
         formik.setFieldValue("swift_code_label", data || "SWIFT Code");
         setSwiftCodeLabel(data);

    }

    const handleSwiftCode = async () => {
        formik.setFieldValue("swift_code_label", swiftCodeLabel);
        setSwiftCodeShow(false);
    }

    const inputRef = useRef();
    const dispatch = useDispatch();
    const { company, transport, ledger, gst } = useSelector(state => state);
    const {
        AccountTypeOptions,
        gstOptions,
        gstCessOptions,
        roundOffOption,
        brokerOptions,
        transportOptions,
        ledgerEntityType,
        brokerageOption,
        ledgerGroupType,
        ledgerDetailOptions,
        ledgerGstOption,
        ledgerGroupOptions,
        billWiseTransactionType
    } = useDropdownOption();
    const companyEntityType = company?.company?.company_tax?.entity_type;
    const [groupLedgerList, setGroupLedgerList] = useState([]);
    const [multiDate, setMultiDate] = useState([]);
    const [openingItemStock, setOpeningItemStock] = useState("");
    const [itemStockList, setItemStockList] = useState([]);
    const [fields, setFields] = useState([]);
    const [companyState, setCompanyState] = useState(company?.company?.billing_address?.state_id);
    const [typingTimeout, setTypingTimeout] = useState(0);
    const [transporterDetail, setTransporterDetail] = useState({
        transport_id: null,
        modelType: false,
    });
    const [filters, setFilters] = useState({
        voucher_number: '',
        voucher_date: '',
        due_date: '',
        transaction_type: '',
        total_amount: '',
        received_amount: '',
        pending_amount: ''
    });
    const originalDataRef = useRef(null);

    useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
    }, [dispatch]);

    useEffect(() => {
        document.getElementById("showName").innerHTML = id ? "Edit Ledger" : "Add Ledger";
    }, []);

    const gstRegistrationType =
        ledgerGstOption &&
        ledgerGstOption?.map(gst => {
            return {
                ...gst,
                isDisabled: formik.values?.party_details?.gstin
                    ? gst.label === "Unregistered" // Disable "Unregistered" if GST is present
                    : gst.label !== "Unregistered",
            };
        });

    useEffect(() => {
        dispatch(getLedgerModelDetail(LedgerType.ADD_LESS_LEDGER));
        dispatch(fetchBrokerList());
        dispatch(fetchTransportList());
        dispatch(fetchCompanyDetails());

        dispatch(fetchGstRateList());
        dispatch(fetchLedgerGroupList());
    }, []);

    const {
        isTransportModel,
        openTransportModel,
        closeTransportModel,
        isItemCatModel,
        openItemCatModel,
        closeItemCatModel,
        isBrokerModel,
        openBrokerModel,
        closeBrokerModel,
        openShippingFromModel,
        isShippingFromModel,
        closeShippingFromModel,
        shippingAddress,
        setShippingAddress,
        selectShippingAddress,
        setSelectShippingAddress,
        isLocationOfAsset,
        openLocationOfAssetModel,
        closeLocationOfAssetModel,
        GSTError,
        setGSTError,
        isChangeGroupType,
        setIsChangeGroupType,
        isShowItemStockModel,
        openStockModel,
        closeStockModel,
        setIsInitialDataLoaded,
        setIsInitialDataLoaded2,
        setHasUnsavedChanges,
        isFieldsChanges,
        setisFieldsChanges,
        isBackButtonClick,
        setIsBackButtonClick,
        setUnsavedBackUrl,
        isNewAddressforLedgerMaster
    } = useContext(StateContext);

    const [isDataStore, setIsDataStore] = useState(false)
    const [countryId, setCountryId] = useState(101);
    const [stateId, setStateId] = useState("");
    const [cityId, setCityId] = useState("");
    const [countryId2, setCountryId2] = useState(101);
    const [cityId2, setCityId2] = useState("");
    const [stateId2, setStateId2] = useState("");
    const [showGstNotUnique, setShowGstNotUnique] = useState(false);
    const [showHoldingRatio, setShowHoldingRatio] = useState(false);
    const [showProfitLossRatio, setShowProfitLossRatio] = useState(false);
    const [isRenderHoldingModel, setIsRenderHoldingModel] = useState(false);
    const [showBulkDeleteModel, setShowBulkDeleteModel] = useState(false);
    const [isRenderProfitLossModel, setIsRenderProfitLossModel] = useState(false);
    const [holdingratioMessage, setHoldingratioMessage] = useState("");
    const [formikValue, setFormikValue] = useState("");
    const [gstNotUniqueMessage, setGstNotUniqueMessage] = useState("");
    const [oldGroupId, setOldGroupId] = useState("");
    const [isUpdateTaxType, setIsUpdateTaxType] = useState(false);
    const [isDeleteItemStock, setIsDeleteItemStock] = useState(false);
    const [editStockDetail, setEditStockDetail] = useState("");
    const [error, setError] = useState({
        hsn_code: "",
        isError: false,
    });
    const [isFocused, setIsFocused] = useState(false);

    const { Country, State, City, Country2, State2, City2 } = CountrySelect({
        countryId,
        setCountryId,
        stateId,
        setStateId,
        cityId,
        setCityId,
        countryId2,
        setCountryId2,
        stateId2,
        setStateId2,
        cityId2,
        setCityId2,
    });
    const [GSTError2, setGSTError2] = useState("");
    const [isFocusedItem, setIsFocusedItem] = useState(false);
    const [descriptionCount, setDescriptionCount] = useState(0);
    const [nameCount, setNameCount] = useState(0);
    const [ledgerList, setLedgerList] = useState(initialValue);
    const [isOpenShippingAddress, setIsOpenShippingAddress] = useState(false);
    const [isAllLedger, setIsAllLedger] = useState(true);
    const [selectedShippingAddressId, setSelectedShippingAddressId] = useState("")
    const [brokerDetail, setBrokerDetail] = useState({
        broker_id: null,
        broker_percentage: null,
        brokerage_on_value: 1,
    });
    const [isFirstRender, setIsFirstRender] = useState(true);
    const [isShowProfitRatio, setIsShowProfitRatio] = useState("");


    useEffect(() => {
        if(!id){
            setStateId(companyState);
            setStateId2(companyState);
        }
    }, [companyState]);

    const handleGroupChange = e => {
        setisFieldsChanges(true);
        setIsChangeGroupType(true);
        dispatch(ifscDetail([]));
        const group = e.label;
        const currentLedgerName = formik.values?.name;
        const currentLedgerId = formik.values?.id;
        const checkParentId = ledgerGroupOptions?.find(ledger => ledger.value == e.value);
        // const getCurrentParentId = checkParentId?.parent_id
        //     ? ledgerGroupOptions?.find(ledger => ledger.value === checkParentId.parent_id)
        //     : checkParentId;
        const getCurrentParentId = checkParentId?.parent_group
            ? { label: checkParentId?.parent_group?.name, value: checkParentId?.parent_group?.id }
            : { label: checkParentId?.label, value: checkParentId?.value };
        const isClearEditData = ledgerGroupOptions?.find(ledger => ledger.value == oldGroupId);
        const resetData = id
            ? isClearEditData?.label !== "Customers" && isClearEditData?.label !== "Supplier"
            : getCurrentParentId
            ? getCurrentParentId?.value !== "Customers" && getCurrentParentId?.value !== "Supplier"
            : group !== "Customers" && group !== "Supplier";
        if (resetData) {
            formik.resetForm();
            setDescriptionCount(0);
        }
        if (
            getCurrentParentId?.label === "Customers" ||
            getCurrentParentId?.label === "Supplier" ||
            getCurrentParentId?.label === "Fixed Asset" ||
            getCurrentParentId?.label === "Fixed Asset 1" ||
            getCurrentParentId?.label === "Income" ||
            getCurrentParentId?.label === "Expense" ||
            getCurrentParentId?.label === "Secured Loan" ||
            getCurrentParentId?.label === "Unsecured Loan" ||
            getCurrentParentId?.label === "Taxes - GST" ||
            getCurrentParentId?.label === "Taxes - TDS" ||
            getCurrentParentId?.label === "Taxes - TCS" ||
            getCurrentParentId?.label === "TDS Receivable" ||
            getCurrentParentId?.label === "TCS Receivable" ||
            getCurrentParentId?.label === "Loan and Advance (Asset)"
        ) {
            dispatch(getLedgerGroupDetail(getCurrentParentId?.value));
        }
        setFields(getCurrentParentId?.label ? [getCurrentParentId?.label] : []);
        setLedgerList({ ...ledgerList, group_id: e.value, action: group });
        formik.setFieldValue("group_id", parseFloat(e.value));
        formik.setFieldValue("parent_action", getCurrentParentId?.label);
        formik.setFieldValue(
            "opening_balance_details.opening_balance_dr_cr",
            group === "Customers" || group === "Income" ? 1 : 2
        );
        formik.setFieldValue("opening_balance_details.opening_balance", "");
        formik.setFieldValue("id", currentLedgerId);
        formik.setFieldValue("name", currentLedgerName);
        formik.setFieldValue("state_id", companyState);
        formik.setFieldValue("address.state_id", companyState);
        formik.setFieldValue("party_details.billing_address.state_id", companyState);
        if (formik.values.party_details?.same_as_billing_address) {
            formik.setFieldValue("party_details.billing_address.state_id", companyState);
        }

        if (getCurrentParentId?.label === "Bank") {
            swiftCodelabels();
        };
    };

    useEffect(() => {
        const defaultGstOption = ledgerGstOption?.find(option => option?.label == "Unregistered");
        if (defaultGstOption && !formik.values?.tax_details?.gst_registration_type) {
            formik.setFieldValue("tax_details.gst_registration_type", defaultGstOption?.value);
        }
    }, [ledgerGstOption]);

    useEffect(() => {
        if (formik.values.parent_action == "Stock in hand") {
            const formattedDates = multiDate?.map(date =>
                moment(date, "DD-MM-YYYY").format("YYYY-MM-DD")
            );
            dispatch(fetchItemStockList(id, formattedDates[0], formattedDates[1]));
        }
    }, [formik.values.parent_action, multiDate]);

    useEffect(() => {
        if (ledger?.itemStockDetail) {
            const response = ledger?.itemStockDetail?.stocks;
            const openingStock = ledger?.itemStockDetail?.opening_balance;
            setOpeningItemStock(openingStock?.amount);
            setItemStockList(response);
        }
    }, [ledger?.itemStockDetail, multiDate]);

    const handleFocusItem = () => setIsFocusedItem(true);
    const handleBlurItem = () => setIsFocusedItem(false);

    useEffect(() => {
        const company_detail = company?.company?.company_tax?.pan_number?.slice(3, 4);
        setIsShowProfitRatio(company_detail);
        setCompanyState(company?.company?.billing_address?.state_id);
        const data = company?.dispatch_address;
        // formik.setFieldValue("state_id", companyState);
        // formik.setFieldValue("address.state_id", companyState);
        setCountryId(data?.country_id || 101);
        if(data?.state_id){
            setStateId(data?.state_id);
        }
        formik.setFieldValue("party_details.billing_address.country_id", data?.country_id || 101);
        formik.setFieldValue(
            "party_details.billing_address.state_id",
            data?.state_id
        );
        const Date = [
            moment(company?.company?.currentFinancialYear?.yearStartDate).format("DD-MM-YYYY"),
            moment(company?.company?.currentFinancialYear?.yearEndDate).format("DD-MM-YYYY"),
        ];
        setMultiDate(Date);
    }, [company, companyState]);

    useEffect(() => {
        if (action && ledgerDetailOptions.length > 0 && isFirstRender && action) {
            const currentGroup = ledgerDetailOptions.find(option => option.label == action);
            formik.setFieldValue("group_id", currentGroup?.value);
            dispatch(getLedgerGroupDetail(currentGroup?.value));
            setIsFirstRender(false);
            setFields([currentGroup?.value]);
        }
    }, [action, ledgerDetailOptions, ledgerGroupDetail]);

    useEffect(() => {
        if (
            formik.values.parent_action &&
            ledgerGroupType &&
            formik.values.group_id &&
            (!id || isChangeGroupType)
        ) {
            let defaultOption = null;

            if (formik.values.parent_action === "Expense") {
                defaultOption = ledgerGroupType?.find(option => option.label === "Purchase");
            } else if (formik.values.parent_action === "Income") {
                defaultOption = ledgerGroupType?.find(option => option.label === "Sales");
            }

            if (defaultOption) {
                formik.setFieldValue("income_type", defaultOption.value);
                formik.setFieldValue("expense_type", defaultOption.value);
            }
        }
    }, [formik.values.parent_action, ledgerGroupType, formik.values.group_id]);

    useEffect(() => {
        formik.setFieldValue("country_id", countryId);
        formik.setFieldValue("state_id", stateId);
        formik.setFieldValue("city_id", cityId);
    }, [countryId, stateId, cityId]);

    useEffect(() => {
        if (companyEntityType === 3 && !id) {
            formik.setFieldValue("holding_ratio", 100);
            formik.setFieldValue("profit_loss_sharing_ratio", 100);
        }
    }, [companyEntityType, formik.values.group_id]);

    useEffect(() => {
        if (id) {
            const checkGroup = groupLedgerList?.find(
                group => group.value == formik.values.parent_id
            );
            const checkExistId = groupLedgerList?.some(
                group => group.value == formik.values.group_id
            );
            // comment: edit ledger in issue some group not add in list
            //checkGroup && checkGroup?.value == oldGroupId &&
            if (!checkExistId && !isAllLedger) {
                setGroupLedgerList(prev => [
                    ...prev,
                    { label: formik.values.new_group_name, value: formik.values.group_id },
                ]);
            } else if (formik.values.new_group_name && isAllLedger) {
                setGroupLedgerList(ledgerDetailOptions);
            }
        }
    }, [ledgerGroupOptions]);

    useEffect(() => {
        if(shippingAddresses?.shippingAddresses?.length > 0 && selectedShippingAddressId && !isNewAddressforLedgerMaster){
            const findShippingAddress = shippingAddress?.findIndex((address) =>
                address.id == selectedShippingAddressId
            )
            setSelectShippingAddress(findShippingAddress)
        }
    }, [shippingAddress])

    useEffect(() => {
        if (fetchLedgerDetail && fetchLedgerDetail?.length !== 0) {
            const response = fetchLedgerDetail?.ledger;
            dispatch(getLedgerGroupDetail(response?.group_id));
            const updateLedgerList = getEditData(response, fetchLedgerDetail);
            const name = ledgerDetailOptions.find(
                item => item.label === response.ledger?.group?.name
            );
            setIsDefault(response?.is_default);
            setOldGroupId(updateLedgerList?.group_id);
            setIsInitialDataLoaded(false)
            setIsInitialDataLoaded2(false)
            setSelectedShippingAddressId(updateLedgerList?.is_selected_address_id)
            const groupList = fetchLedgerDetail?.group_lists
                ? Object.entries(fetchLedgerDetail?.group_lists).map(([key, value]) => {
                      return {
                          label: value,
                          value: key,
                      };
                  })
                : [];
            setGroupLedgerList(groupList);
            setIsAllLedger(fetchLedgerDetail?.is_all_ledger ? true : false);
            setFields([name]);
            if (updateLedgerList?.party_details) {
                dispatch(fetchShippingAddressList(updateLedgerList?.id));
                setCountryId(updateLedgerList?.party_details?.billing_address?.country_id || 101);
                setStateId(
                    updateLedgerList?.party_details?.billing_address?.state_id
                );
                setCityId(updateLedgerList?.party_details?.billing_address?.city_id);
            }
            if (updateLedgerList?.address) {
                setCountryId(updateLedgerList?.address?.country_id || 101);
                setStateId(updateLedgerList?.address?.state_id);
                setCityId(updateLedgerList?.address?.city_id);
            }
            setCountryId2(updateLedgerList?.party_details?.shipping_address?.country_id || 101);
            setStateId2(
                updateLedgerList?.party_details?.shipping_address?.state_id
            );
            setCityId2(updateLedgerList?.party_details?.shipping_address?.city_id);
            if (updateLedgerList?.action == "Capital") {
                setCountryId(updateLedgerList?.country_id);
                setStateId(updateLedgerList?.state_id);
                setCityId(updateLedgerList?.city_id);
                formik.setFieldValue("address_1", updateLedgerList?.address_1);
                formik.setFieldValue("address_2", updateLedgerList?.address_2);
            }
            formik.setFieldValue("id", updateLedgerList?.id);
            formik.setFieldValue("name", updateLedgerList?.name);
            setNameCount(updateLedgerList?.name?.length);
            formik.setFieldValue("group_id", updateLedgerList?.group_id);
            formik.setFieldValue("is_use_in_other_companies", updateLedgerList?.is_use_in_other_companies);
            formik.setFieldValue("action", updateLedgerList?.action);
            formik.setFieldValue("party_details.gstin", updateLedgerList?.gstin);
            formik.setFieldValue("parent_action", updateLedgerList?.action);
            formik.setFieldValue("is_gst_applicable", updateLedgerList?.is_gst_applicable);
            formik.setFieldValue("gst_tax_id", updateLedgerList?.gst_tax_id);
            formik.setFieldValue("gst_cess_rate", updateLedgerList?.gst_cess_rate);
            formik.setFieldValue("hsn_sac_code", updateLedgerList?.hsn_sac_code);
            formik.setFieldValue("description", updateLedgerList?.description);
            setDescriptionCount(updateLedgerList?.description?.length);
            formik.setFieldValue(
                "other_details.allow_credit_limit",
                updateLedgerList?.allow_credit_limit
            );
            formik.setFieldValue("other_details.credit_limit", updateLedgerList?.credit_limit);
            formik.setFieldValue("other_details", updateLedgerList?.other_details);
            formik.setFieldValue("pan", updateLedgerList?.pan);
            formik.setFieldValue("individual_huf", updateLedgerList?.individual_huf);
            formik.setFieldValue("location_of_asset_id", updateLedgerList?.location_of_asset_id);
            formik.setFieldValue("for_other", updateLedgerList?.for_other);
            formik.setFieldValue("pan_not_given", updateLedgerList?.pan_not_given);
            formik.setFieldValue("individual_bill_wise", updateLedgerList?.individual_bill_wise);
            formik.setFieldValue("yearly_total", updateLedgerList?.yearly_total);
            formik.setFieldValue("rounding_method", updateLedgerList?.rounding_method);
            formik.setFieldValue("calculated_on", updateLedgerList?.calculated_on);
            formik.setFieldValue("unsecured_loan_type", updateLedgerList?.unsecured_loan_type);
            formik.setFieldValue(
                "unsecured_loan_account_number",
                updateLedgerList?.unsecured_loan_account_number
            );
            formik.setFieldValue(
                "unsecured_loan_name_of_financier",
                updateLedgerList?.unsecured_loan_name_of_financier
            );
            formik.setFieldValue(
                "unsecured_loan_rate_of_annum",
                updateLedgerList?.unsecured_loan_rate_of_annum
            );
            formik.setFieldValue(
                "unsecured_loan_pan_number",
                updateLedgerList?.unsecured_loan_pan_number
            );
            formik.setFieldValue("account_holder_name", updateLedgerList?.account_holder_name);
            formik.setFieldValue("account_number", updateLedgerList?.account_number);
            formik.setFieldValue("account_type", updateLedgerList?.account_type);
            formik.setFieldValue("ifsc_code", updateLedgerList?.ifsc_code);
            formik.setFieldValue("bank_name", updateLedgerList?.bank_name);
            formik.setFieldValue("bank_branch", updateLedgerList?.bank_branch);
            formik.setFieldValue("upi_id", updateLedgerList?.upi_id);
            formik.setFieldValue("swift_code", updateLedgerList?.swift_code);
            formik.setFieldValue("rounding_method", updateLedgerList?.rounding_method);
            formik.setFieldValue("tax_type", updateLedgerList?.tax_type);
            formik.setFieldValue("name_of_financier", updateLedgerList?.name_of_financier);
            formik.setFieldValue("secured_loan_type", updateLedgerList?.secured_loan_type);
            formik.setFieldValue("loan_account_number", updateLedgerList?.loan_account_number);
            formik.setFieldValue("rate_of_annum", updateLedgerList?.rate_of_annum);
            formik.setFieldValue("party_details", updateLedgerList?.party_details);
            formik.setFieldValue("bill_wise_opening_balance_details", updateLedgerList?.bill_wise_opening_balance_details?.length > 0 ? updateLedgerList?.bill_wise_opening_balance_details : formik.values.bill_wise_opening_balance_details);
            formik.setFieldValue("tax_details", updateLedgerList?.tax_details);
            formik.setFieldValue("other_details", updateLedgerList?.other_details);
            formik.setFieldValue("holding_ratio", updateLedgerList?.holding_ratio);
            formik.setFieldValue("name_of_proprietor", updateLedgerList?.name_of_proprietor);
            formik.setFieldValue(
                "profit_loss_sharing_ratio",
                updateLedgerList?.profit_loss_sharing_ratio
            );
            formik.setFieldValue(
                "party_details.shipping_address.party_name_same_as_address_name",
                updateLedgerList?.party_details?.party_name_same_as_address_name
            );
            formik.setFieldValue("pan_number", updateLedgerList?.pan_number);
            formik.setFieldValue("pin_code", updateLedgerList?.pin_code);
            formik.setFieldValue("aadhar_number", updateLedgerList?.aadhar_number);
            formik.setFieldValue(
                "party_details.same_as_billing_address",
                updateLedgerList?.party_details?.same_as_billing_address
            );
            formik.setFieldValue("income_type", updateLedgerList?.income_type);
            formik.setFieldValue("expense_type", updateLedgerList?.expense_type);
            formik.setFieldValue("address", updateLedgerList?.address);
            formik.setFieldValue("contact_details", updateLedgerList?.contact_details);
            formik.setFieldValue(
                "opening_balance_details",
                updateLedgerList?.opening_balance_details
            );
            setIsDataStore(true);

            if (updateLedgerList?.action === "Bank") {
                swiftCodelabels(updateLedgerList?.swift_code_label);
            };

        }
    }, [fetchLedgerDetail]);

    useEffect(() => {
        if (shippingAddress[selectShippingAddress] && isOpenShippingAddress) {
            formik.setFieldValue(
                "party_details.shipping_address.shipping_gstin",
                shippingAddress[selectShippingAddress]?.gstin || shippingAddress[selectShippingAddress]?.shipping_gstin || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.shipping_name",
                shippingAddress[selectShippingAddress]?.party || shippingAddress[selectShippingAddress]?.shipping_name || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_id",
                shippingAddress[selectShippingAddress]?.id
            );
            formik.setFieldValue(
                "party_details.shipping_address.party_name_same_as_address_name",
                shippingAddress[selectShippingAddress]?.party_name_same_as_address_name ?? null
            );
            if(shippingAddress[selectShippingAddress]?.party_name_same_as_address_name == true){
                formik.setFieldValue(
                    "party_details.shipping_address.address_name",
                    shippingAddress[selectShippingAddress]?.shipping_name ?? ""
                );
            }else{
                formik.setFieldValue("party_details.shipping_address.address_name", shippingAddress[selectShippingAddress]?.address_name ?? "");
            }
            formik.setFieldValue(
                "party_details.shipping_address.address_1",
                shippingAddress[selectShippingAddress]?.address_1 || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_2",
                shippingAddress[selectShippingAddress]?.address_2 || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                shippingAddress[selectShippingAddress]?.country_id || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.state_id",
                shippingAddress[selectShippingAddress]?.state_id ?? companyState
            );
            formik.setFieldValue(
                "party_details.shipping_address.city_id",
                shippingAddress[selectShippingAddress]?.city_id || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.pin_code",
                shippingAddress[selectShippingAddress]?.pin_code || ""
            );
            setCountryId2(shippingAddress[selectShippingAddress]?.country_id || 101);
            setStateId2(shippingAddress[selectShippingAddress]?.state_id ?? companyState);
            setCityId2(shippingAddress[selectShippingAddress]?.city_id);
        }
    }, [shippingAddress[selectShippingAddress], isOpenShippingAddress, selectShippingAddress]);

    useEffect(() => {
        const gstData = gst?.gst;
        if (gstData.length !== 0 && formik.values?.party_details.gstin) {
            const pan_card = formik.values?.party_details.gstin.slice(2, 12);
            const gst_registration = gstData?.result?.dty;
            const fing_Gst_name = ledgerGstOption.find(name =>
                name?.label.includes(gst_registration)
            );
            formik.setFieldValue("tax_details.gst_registration_type", fing_Gst_name?.value || 1);
            formik.setFieldValue("tax_details.pan", pan_card);
            const entity_type = pan_card.slice(3, 4);
            dispatch(fetchEntityData(entity_type, formik, "tax_details.type_of_entity"));
            formik.setFieldValue("tax_details.type_of_entity_character", entity_type);
            const addr = gstData?.result?.pradr?.addr;

            let address1 = addr?.bno || "";

            if (addr?.flno) {
                address1 += ", " + addr?.flno;
            }

            if (addr?.bnm) {
                address1 += ", " + addr?.bnm;
            }

            let address2 = addr?.st || "";

            if (addr?.landMark) {
                address2 += ", " + addr?.landMark;
            }

            if (addr?.locality) {
                address2 += ", " + addr?.locality;
            }
            let pinCode = gstData?.result?.pradr?.addr?.pncd;
            let state = gstData?.result?.pradr?.addr?.stcd;
            let city = gstData?.result?.pradr?.addr?.dst;
            const response = fetchGstDetail({ state, city });
            formik.setFieldValue("party_details.billing_address.address_1", address1 || "");
            formik.setFieldValue("name", convertNameIntoCamelCase(gstData?.result?.tradeNam));
            setNameCount(gstData?.result?.tradeNam?.length);
            formik.setFieldValue("party_details.billing_address.address_2", address2 || "");
            formik.setFieldValue("party_details.billing_address.country_id", response.country_id);
            formik.setFieldValue("party_details.billing_address.state_id", response.state_id);
            formik.setFieldValue("party_details.billing_address.city_id", response.city_id);
            formik.setFieldValue("party_details.billing_address.pin_code", pinCode);
            setCountryId(response.country_id || 101);
            setStateId(response.state_id ?? companyState);
            setCityId(response.city_id);
            if (formik.values?.party_details?.same_as_billing_address == true) {
                formik.setFieldValue("party_details.shipping_address.shipping_gstin", formik.values.party_details?.gstin);
                formik.setFieldValue("party_details.shipping_address.shipping_name", convertNameIntoCamelCase(gstData?.result?.tradeNam));
                formik.setFieldValue("party_details.shipping_address.address_1", address1);
                formik.setFieldValue("party_details.shipping_address.address_2", address2);
                formik.setFieldValue(
                    "party_details.shipping_address.country_id",
                    response.country_id
                );
                formik.setFieldValue(
                    "party_details.shipping_address.state_id",
                    response.state_id ?? companyState
                );
                formik.setFieldValue("party_details.shipping_address.city_id", response.city_id);
                formik.setFieldValue("party_details.shipping_address.pin_code", pinCode);
                formik.setFieldValue("party_details.same_as_shipping_address.address_1", address1);
                formik.setFieldValue("party_details.same_as_shipping_address.address_2", address2);
                formik.setFieldValue(
                    "party_details.same_as_shipping_address.country_id",
                    response.country_id
                );
                formik.setFieldValue(
                    "party_details.same_as_shipping_address.state_id",
                    response.state_id ?? companyState
                );
                formik.setFieldValue(
                    "party_details.same_as_shipping_address.city_id",
                    response.city_id
                );
                formik.setFieldValue("party_details.same_as_shipping_address.pin_code", pinCode);
                setCountryId2(response.country_id || 101);
                setStateId2(response.state_id ?? companyState);
                setCityId2(response.city_id);
            }
        }
        setTimeout(() => {
            dispatch(gstDetail(""));
        }, 500);
    }, [gst?.gst, ledgerGstOption]);

    useEffect(() => {
        const gstData = gst?.shippingGst;
        if (gstData.length !== 0) {
            let address1 =
                gstData?.result?.pradr?.addr?.bno +
                ", " +
                gstData?.result?.pradr?.addr?.flno +
                ", " +
                gstData?.result?.pradr?.addr?.bnm;
            let address2 = gstData?.result?.pradr?.addr?.st;
            let pinCode = gstData?.result?.pradr?.addr?.pncd;
            let state = gstData?.result?.pradr?.addr?.stcd;
            let city = gstData?.result?.pradr?.addr?.dst;
            const response = fetchGstDetail({ state, city });
            formik.setFieldValue("party_details.shipping_address.address_1", address1);
            formik.setFieldValue(
                "party_details.shipping_address.shipping_name",
                convertNameIntoCamelCase(gstData?.result?.tradeNam)
            );
            if(formik.values?.party_details.shipping_address.party_name_same_as_address_name){
                formik.setFieldValue(
                    "party_details.shipping_address.address_name",
                    convertNameIntoCamelCase(gstData?.result?.tradeNam)
                );
            }
            setNameCount(gstData?.result?.tradeNam?.length);
            formik.setFieldValue("party_details.shipping_address.address_2", address2);
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                response.country_id || 101
            );
            formik.setFieldValue(
                "party_details.shipping_address.state_id",
                response.state_id ?? companyState
            );
            formik.setFieldValue("party_details.shipping_address.city_id", response.city_id);
            formik.setFieldValue("party_details.shipping_address.pin_code", pinCode);
            setCountryId2(response.country_id || 101);
            setStateId2(response.state_id ?? companyState);
            setCityId2(response.city_id);
        }
        setTimeout(() => {
            dispatch(shippingGst(""));
        }, 500);
    }, [gst?.shippingGst]);

    useEffect(() => {
        const response = ledger?.ifscDetail;
        if (response.length != 0) {
            formik.setFieldValue("bank_branch", response?.BRANCH);
            formik.setFieldValue("bank_name", response?.BANK);
        }
    }, [ledger?.ifscDetail, formik.values?.ifsc_code]);

    function validateFiles(e) {
        const maxFileSize = 2 * 1024 * 1024;
        const files = Array.from(e.currentTarget.files);

        const validFiles = Array.from(e.currentTarget?.files || []).filter(
            file => file?.size <= maxFileSize
        );

        if (files.length > 5 || formik.values?.other_details?.upload_document?.length >= 5) {
            dispatch(
                errorToast({
                    text: "Please Enter Max 5 Documents",
                    type: toastType.ERROR,
                })
            );
            e.currentTarget.value = "";
            return;
        } else if (validFiles.length < (e.currentTarget?.files || []).length) {
            dispatch(
                errorToast({
                    text: "Please Select a file less than 2MB",
                    type: toastType.ERROR,
                })
            );
            e.target.value = "";
            return;
        }

        files?.map((file, index) => {
            if (formik.values?.parent_action === "Supplier") {
                formik.setFieldValue(`other_details.[supplier_upload_document]${[index]}`, file);
            } else {
                formik.setFieldValue(`other_details.[customer_upload_document]${[index]}`, file);
            }
        });
    }

    const handlePhoneChange = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        formik.setFieldValue("party_details.region_iso_1", country.countryCode);
        formik.setFieldValue("party_details.region_code_1", country.dialCode);
        formik.setFieldValue("party_details.contact_person_phone_1", number);
        formik.setFieldValue("party_details.contact_person_phone_input_1", value);
    };

    const handleHSNCode = e => {
        const { value } = e.target;

        if (formik.values.item_type == 1 && !/^\d*$/.test(value)) {
            return;
        }

        if (value.length > 8) {
            return;
        }

        formik.setFieldValue("hsn_sac_code", value);
    };
    const handlePhoneChange2 = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        formik.setFieldValue("party_details.region_iso_2", country.countryCode);
        formik.setFieldValue("party_details.region_code_2", country.dialCode);
        formik.setFieldValue("party_details.contact_person_phone_2", number);
        formik.setFieldValue("party_details.contact_person_phone_input_2", value);
    };

    const handleSubmit = async values => {
        const submitType = values.submitType;
        const response = await handleSubmitLedger(values, companyState); // Ensure `handleSubmitLedger` is awaited if it returns a promise.
        setFormikValue(values);

        if (response.action === "Capital") {
            const fetchCapitalRatioDetail = await dispatch(
                checkCapitalLedgerRatio(
                    response.id,
                    response.holding_ratio,
                    response.profit_loss_sharing_ratio
                )
            );

            // Handle Holding Ratio Modal
            if (fetchCapitalRatioDetail?.forHolding?.model && !isRenderHoldingModel) {
                setShowHoldingRatio(true); // Open holding ratio modal
                setIsRenderHoldingModel(true);
                setHoldingratioMessage(
                    "Already Holding Ratio divided. Are you sure you want to Add Account?"
                );
                return; // Exit here to wait for modal confirmation.
            }

            // Handle Profit Loss Ratio Modal
            if (fetchCapitalRatioDetail?.forProfitLoss?.model && !isRenderProfitLossModel) {
                setShowProfitLossRatio(true); // Open profit/loss ratio modal
                setIsRenderProfitLossModel(true);
                setHoldingratioMessage(
                    "Already Profit Loss divided. Are you sure you want to Add Account?"
                );
                return; // Exit here to wait for modal confirmation.
            }

            // Submit the form based on the event type
            handleFinalSubmission(response, submitType);
        } else {
            // Directly submit the form if action is not "Capital"
            handleFinalSubmission(response, submitType);
        }
        setisFieldsChanges(false);
        setHasUnsavedChanges(false);
    };

    const handleFinalSubmission = (response, type) => {
        setIsDisable(true);
        const redirectType = searchParams.get("ledgerType")
        if (!id) {
            dispatch(addMasterLedger(response, formik.values.submitType, setIsDisable, redirectType));
        } else {
            dispatch(updateMasterLedger(id, response, setIsDisable, redirectType));
        }
    };

    // Modal Confirmation Handlers
    const handleHoldingConfirmation = () => {
        setShowHoldingRatio(false); // Close holding modal
        handleSubmit(formikValue, "holding"); // Trigger the next step
    };

    const handleProfitLossConfirmation = () => {
        setShowProfitLossRatio(false); // Close profit/loss modal
        handleSubmit(formikValue, "profit"); // Trigger form submission
    };

    useEffect(() => {
        if (formik.values?.party_details?.billing_address) {
            handleSameAsBill({
                target: {
                    checked: formik.values?.party_details?.same_as_billing_address,
                },
            });
        }
    }, [
        formik.values?.party_details?.billing_address,
        formik.values?.party_details?.same_as_billing_address,
    ]);

    const handleSameAsBill = (e, type) => {
        const { checked } = e.target;
        formik.setFieldValue("party_details.same_as_billing_address", checked);
        if (checked) {
            formik.setFieldValue(
                "party_details.shipping_address.address_1",
                formik.values?.party_details?.billing_address?.address_1
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_2",
                formik.values?.party_details?.billing_address?.address_2
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_name",
                formik.values.name
            );
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                formik.values?.party_details?.billing_address?.country_id
            );
            formik.setFieldValue(
                "party_details.shipping_address.state_id",
                formik.values?.party_details?.billing_address?.state_id
            );
            formik.setFieldValue(
                "party_details.shipping_address.city_id",
                formik.values?.party_details?.billing_address?.city_id
            );
            formik.setFieldValue(
                "party_details.shipping_address.pin_code",
                formik.values?.party_details?.billing_address?.pin_code
            );
        } else if (type === "shipping" && !checked) {
            formik.setFieldValue("party_details.shipping_address.address_1", null);
            formik.setFieldValue("party_details.shipping_address.address_2", null);
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                formik.values?.party_details?.shipping_address?.country_id
            );
            formik.setFieldValue("party_details.shipping_address.state_id", null);
            formik.setFieldValue("party_details.shipping_address.city_id", null);
            formik.setFieldValue("party_details.shipping_address.pin_code", null);
            formik.setFieldValue("party_details.shipping_address.shipping_gstin", null);
            formik.setFieldValue("party_details.shipping_address.shipping_name", null);
            formik.setFieldValue("party_details.shipping_address.address_name", null);
            setStateId2("");
            setCityId2("");
        }
    };

    const handleOpenTransportModel = id => {
        openTransportModel();
        if (id) {
            dispatch(fetchTransportById(id));
            setTransporterDetail({
                transporter_id: id,
                modelType: true,
            });
        } else {
            setTransporterDetail({
                transporter_id: "",
                modelType: false,
            });
        }
    };

    const handleOpenStockModel = data => {
        openStockModel();
        setEditStockDetail(data);
    };

    const handleDateChange = (date) => {
        if (!date || date.length === 0) return;
        const DateFormatted = [
            moment(date[0]).format("DD-MM-YYYY"),
            date[1] ? moment(date[1]).format("DD-MM-YYYY") : null,
        ].filter(Boolean);
        setMultiDate(DateFormatted);
    };

    const handleChangeDueDate = (date, index, type) =>{
        const isValidDate = date && !isNaN(new Date(date).getTime());
        const formattedDateValue = isValidDate ? formattedDate(new Date(date)) : "";
        if(type == "voucher"){
            formik.setFieldValue(`bill_wise_opening_balance_details.${index}.voucher_date`, formattedDateValue);
        }else{
            formik.setFieldValue(`bill_wise_opening_balance_details.${index}.due_date`, formattedDateValue);
        }
    }

    const handleDeleteStockModel = data => {
        setIsDeleteItemStock(true);
        setEditStockDetail(data);
    };

    const handleDeleteItemStock = async () => {
        const formattedDates = multiDate?.map(date =>
            moment(date, "DD-MM-YYYY").format("YYYY-MM-DD")
        );
        dispatch(
            deleteItemStock(
                editStockDetail?.id,
                editStockDetail,
                formattedDates[0],
                formattedDates[1]
            )
        );
        setIsDeleteItemStock(false);
    };

    useEffect(() => {
        if (shippingAddresses?.shippingAddresses?.length > 0) {
            const shippingList = shippingAddresses?.shippingAddresses;
            setShippingAddress(shippingList);
        }
    }, [shippingAddresses?.shippingAddresses]);

    const changeGstNumber = async e => {
        const { value } = e.target;
        const upperValue = value.trim().toUpperCase();
        formik.setFieldValue("party_details.gstin", upperValue);

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            dispatch(fetchGstData(upperValue));
            const response = await checkGstNumberAlreadyUsed(upperValue, id);
            if (response?.is_not_unique_gst_number) {
                setShowGstNotUnique(true);
                setGstNotUniqueMessage(response?.massage);
            }
            setGSTError("");
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            formik.setFieldValue("tax_details.pan", "");
            formik.setFieldValue("tax_details.type_of_entity", "");
            const defaultGstOption = ledgerGstOption?.find(
                option => option?.label == "Unregistered"
            );
            if (defaultGstOption) {
                formik.setFieldValue("tax_details.gst_registration_type", defaultGstOption?.value);
            }
            setGSTError("");
        } else {
            setGSTError("Please enter a valid GSTIN");
        }
    };

    const changeShippingGstNumber = e => {
        const { value } = e.target;
        const upperValue = value.toUpperCase();
        formik.setFieldValue("party_details.shipping_address.shipping_gstin", upperValue);

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            dispatch(fetchShippingGstData(upperValue));
            setGSTError2(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError2("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            setGSTError2(""); // Clear error if input is cleared
        } else {
            setGSTError2("Please enter a valid GSTIN");
        }
    };

    const changePanDetail = e => {
        const { value, selectionStart } = e.target;
        const entity_type = value.slice(3, 4);
        dispatch(fetchEntityData(entity_type, formik, "tax_details.type_of_entity"));
        formik.setFieldValue("tax_details.pan", value?.toUpperCase());
        formik.setFieldValue("tax_details.type_of_entity_character", entity_type);

        setTimeout(() => {
            e.target.selectionStart = e.target.selectionEnd = selectionStart;
        }, 0);
    };
    const changeTanDetail = e => {
        const { value } = e.target;
        formik.setFieldValue("tax_details.tan", value);
    };

    const changeCinDetail = e => {
        const { value } = e.target;
        formik.setFieldValue("tax_details.cin_number", value);
    };

    const handleBrokerModel = () => {
        openBrokerModel();
        dispatch(getBrokerModelDetail());
    };

    const changeIfscCode = e => {
        const { value } = e.target;
        formik.setFieldValue("ifsc_code", value);
        if (value) {
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }
            setTypingTimeout(
                setTimeout(() => dispatch(fetchBankDetail(value)), 500)
            );
        } else {
            dispatch(ifscDetail(""));
            formik.setFieldValue("bank_branch", "");
            formik.setFieldValue("bank_name", "");
        }
    };

    const handleOpenShippingAddressModal = () => {
        openShippingFromModel();
        setIsOpenShippingAddress(true);
    };

    const handleCreditLimitChange = event => {
        formik.setFieldValue("other_details.allow_credit_limit", event);
    };
    const changeCreditLimitAction = event => {
        formik.setFieldValue("other_details.credit_limit_action", event);
    };

    const handleChangeTaxType = e => {
        setIsUpdateTaxType(true);
        formik.setFieldValue("tax_type", e.value);
    };

    const changeBrokerDetail = e => {
        setisFieldsChanges(true);
        if (e.value) {
            formik.setFieldValue("other_details.broker", e.value);
            dispatch(fetchBrokerDetailsById(e.value, formik, false));
        } else {
            formik.setFieldValue("other_details.broker", "");
            formik.setFieldValue("other_details.brokerage_on_value", "");
            formik.setFieldValue("other_details.brokerage_percentage", "");
        }
    };

    const fetchTcsTdsTaxDetail = async () => {
        let response = null;
        if (formik.values?.parent_action.includes("TCS")) {
            response = await dispatch(fetchTcsRateDetail(formik.values?.tax_type));
        } else {
            response = await dispatch(fetchTdsRateDetail(formik.values?.tax_type));
        }
        formik.setFieldValue("individual_huf", response?.for_individual_huf || 0);
        formik.setFieldValue("for_other", response?.for_other || 0);
        formik.setFieldValue("pan_not_given", response?.pan_not_given || 0);
        formik.setFieldValue("individual_bill_wise", response?.individual_bill_wise || 0);
    };

    const handleChangeAddressName = e => {
        const { value } = e.target;
        formik.setFieldValue("party_details.shipping_address.shipping_name", value);
        if (formik.values?.party_details?.shipping_address?.party_name_same_as_address_name) {
        formik.setFieldValue("party_details.shipping_address.address_name", value);
        }
    };

    const handleSameAsAddressName = e => {
        const { checked } = e.target;
        formik.setFieldValue(
            "party_details.shipping_address.address_name",
            checked ? formik.values.party_details.shipping_address.shipping_name || "" : ""
        );
        formik.setFieldValue(
            "party_details.shipping_address.party_name_same_as_address_name",
            checked
        );
    };

    useEffect(() => {
        if (formik.values?.tax_type && isUpdateTaxType) {
            fetchTcsTdsTaxDetail();
        }
    }, [formik.values?.tax_type]);

    const changeLedgerName = value => {
        if (formik.values.parent_action == "Capital") {
            formik.setFieldValue("name_of_proprietor", value);
        }
        formik.setFieldValue("name", value);
        setNameCount(value?.length);
    };

    useEffect(() => {
        const companyEntityType = company?.company?.company_tax?.entity_type;
        if((companyEntityType == 1 || companyEntityType == 2 || companyEntityType === null) && !formik.values.id && formik.values.name){
            formik.setFieldValue("name_of_proprietor", formik.values?.name);
        }
    }, [formik.values?.parent_action, company?.company, formik.values.id]);

    useEffect(() => {
        if (window.location.pathname.includes("ledgers/create")) {
            setTimeout(() => {
                setLoader(false);
            }, 1000);
        }
    }, []);

    useEffect(() => {
        const ledgerType = searchParams.get("ledgerType");

        if (ledgerType && ledgerDetailOptions?.length > 0) {
            const ledgerDetail = ledgerDetailOptions.find(item => item.label === ledgerType);
            formik.setFieldValue("group_id", parseFloat(ledgerDetail.value));
            formik.setFieldValue("parent_action", ledgerDetail.label);
        };

    }, [ledgerDetailOptions])

    const handleBack = () =>{
        const redirectType = searchParams.get("ledgerType");
        if(isFieldsChanges) {
            setIsBackButtonClick(false);
            setHasUnsavedChanges(true);
            if(redirectType){
                if(redirectType == "Supplier"){
                    setUnsavedBackUrl(`${window.location.origin}/company/supplier-master`);
                }else{
                    setUnsavedBackUrl(`${window.location.origin}/company/customer-master`);
                }
            }else{
                setUnsavedBackUrl(`${window.location.origin}/company/ledgers`)
            }
        } else {
            if(redirectType){
                if(redirectType == "Supplier"){
                    window.location.href = `${window.location.origin}/company/supplier-master`;
                }else{
                    window.location.href = `${window.location.origin}/company/customer-master`;
                }
            }else{
                window.location.href = `${window.location.origin}/company/ledgers`
            }
        }
    }

    const handleInput = (e) => {
        setisFieldsChanges(true);
    };

    const handleBeforeUnload = (e) => {
        if (isFieldsChanges && isBackButtonClick) {
            e.preventDefault();
            e.returnValue = '';
        }
    };

    useEffect(() => {
        if (isBackButtonClick) {
                window.addEventListener('beforeunload', handleBeforeUnload);
            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        };
    }, [isFieldsChanges, isBackButtonClick]);
    const handleCheckboxChange = (e) => {
        const check_opening_balance_used = formik.values.bill_wise_opening_balance_details?.filter((balance) => !balance?.is_editable);
        if(check_opening_balance_used?.length > 0) {
            dispatch(
                errorToast({
                    text: "This billed cannot be deleted because it is already used in transactions.",
                    type: toastType.ERROR,
                })
            )
            return
        }
        formik.setFieldValue("opening_balance_details.bill_wise", e.target.checked ? 1 : 0);
        formik.setFieldValue("opening_balance_details.bill_wise_opening_balance_details", Array(1).fill({
            voucher_number: "",
            voucher_date: "",
            due_date: "",
            transaction_type: null,
            total_amount: "",
            received_amount: "",
            pending_amount: "",
        }));
    };
    const handleAddRow = () => {
        const newRow = {
                voucher_number: "",
                voucher_date: "",
                due_date: "",
                transaction_type: null,
                total_amount: "",
                received_amount: "",
                pending_amount: "",
                is_editable: true
        }
        const updatedData = [...formik.values.bill_wise_opening_balance_details, newRow];
        formik.setFieldValue("bill_wise_opening_balance_details", updatedData);
        originalDataRef.current = [...originalDataRef.current, newRow];
    };
    const handleDeleteRow = (deleteIndex, event) => {
        event.preventDefault();

        const updatedData = formik.values.bill_wise_opening_balance_details.filter((_, index) => index !== deleteIndex);
        formik.setFieldValue("bill_wise_opening_balance_details", updatedData);
        const updatedRows = selectedRows
          .filter(index => index !== deleteIndex) // remove the deleted row
          .map(index => (index > deleteIndex ? index - 1 : index)); // shift remaining

        setSelectedRows(updatedRows);
      };

    const handleSelectChange = (selected, index) => {
        const updatedRows = formik.values.bill_wise_opening_balance_details.map((row, i) =>
            i === index ? { ...row, transaction_type: selected.value } : { ...row }
        );
        formik.setFieldValue("bill_wise_opening_balance_details", updatedRows);
    };

    useEffect(() => {
        if(formik.values?.bill_wise_opening_balance_details?.length){
            setPendingAmount(formik.values?.bill_wise_opening_balance_details?.reduce((total, item) => total + (item.pending_amount || 0), 0));
            setReceivedAmount(formik.values?.bill_wise_opening_balance_details?.reduce((total, item) => total + (formik.values.parent_action?.toLowerCase() === "supplier" ? item.paid_amount || 0 : item.received_amount || 0), 0));
            setTotalAmount(formik.values?.bill_wise_opening_balance_details?.reduce((total, item) => total + (item.total_amount || 0), 0));
            const DrAmount = formik.values?.bill_wise_opening_balance_details?.filter((item) => [1,3,6,7]?.includes(item.transaction_type))?.reduce((total, item) => total + item.pending_amount, 0);
            const CrAmount = formik.values?.bill_wise_opening_balance_details?.filter((item) => [2,4,5,8]?.includes(item.transaction_type))?.reduce((total, item) => total + item.pending_amount, 0);
            if(DrAmount > CrAmount){
                setDrCrAmount(DrAmount - CrAmount)
                formik.setFieldValue("opening_balance_details.opening_balance", DrAmount - CrAmount)
                setIsShowDrCr("Dr")
                formik.setFieldValue("opening_balance_details.opening_balance_dr_cr", 1)
            }else{
                setDrCrAmount(CrAmount - DrAmount)
                formik.setFieldValue("opening_balance_details.opening_balance", CrAmount - DrAmount)
                setIsShowDrCr(CrAmount - DrAmount == 0 ? "Dr" : "Cr")
                formik.setFieldValue("opening_balance_details.opening_balance_dr_cr", CrAmount - DrAmount == 0 ? 1 : 2)
            }
        }
    }, [formik.values.bill_wise_opening_balance_details])

    const handleBillWiseChange = (e, index, type) => {
        const { name, value } = e.target;
        if (type === "received_amount" || type === "paid_amount") {
            if (formik.values.bill_wise_opening_balance_details[index].total_amount >= value) {
                formik.setFieldValue(name, parseFloat(value || ""));
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].pending_amount`, formik.values.bill_wise_opening_balance_details[index].total_amount - value,
                );
            }
        } else if (type === "total_amount") {
            if(formik.values.bill_wise_opening_balance_details[index].received_amount == ''){
                formik.setFieldValue(`bill_wise_opening_balance_details[${index}].received_amount`, 0)
            }
            if(parseFloat(value) >= parseFloat((formik.values.bill_wise_opening_balance_details[index].received_amount || 0))){
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].pending_amount`,
                    value - (formik.values.bill_wise_opening_balance_details[index].received_amount || 0)
                );
            }else{
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].pending_amount`,
                    0
                );
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].received_amount`,
                    0
                );
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].paid_amount`,
                    0
                );
            }
            formik.setFieldValue(name, parseFloat(value || ""));
        } else if(name?.includes("voucher_number")) {
            formik.setFieldValue(name, value || "");
        }else{
            formik.setFieldValue(name, parseFloat(value || ""));
        }
    };

    const [selectedRows, setSelectedRows] = useState([]);
    const handleSelectAll = (e) => {
        if (e.target.checked) {
            setSelectedRows(
                formik.values.bill_wise_opening_balance_details
                  .map((item, index) => ({ index, isEditable: item?.is_editable }))
                  .filter(item => item.isEditable)
                  .map(item => item.index)
              );

        } else {
            setSelectedRows([]);
        }
    };
    const handleRowSelect = (index) => {
        setSelectedRows((prevSelected) =>
            prevSelected.includes(index)
                ? prevSelected.filter((i) => i !== index)
                : [...prevSelected, index]
        );
    };
    const handleBulkDelete = () => {
        setShowBulkDeleteModel(true)
    };
    const handleConfirmBulkDelete = () => {
        setSelectedRows([]);
        setShowBulkDeleteModel(false)
        formik.setFieldValue("bill_wise_opening_balance_details", formik.values.bill_wise_opening_balance_details.filter((_, index) => !selectedRows.includes(index)));
        dispatch(
            addToast({
                text: "Record deleted successfully",
                type: toastType.ADD_TOAST,
            })
        )
    };

    useEffect(() => {
        const details = formik.values?.bill_wise_opening_balance_details;
        const allFiltersEmpty = Object.values(filters).every((val) => val.trim?.() === "");

        if (Array.isArray(details)) {
            if (details.length > 0 && isDataStore) {
                originalDataRef.current = [...details];
                setIsDataStore(false);
            } else if (!originalDataRef.current?.length || allFiltersEmpty) {
                originalDataRef.current = [...details];
            }
        }
    }, [formik.values?.bill_wise_opening_balance_details, isDataStore]);

    const customFilter = (e, type) =>{
        const { value } = e.target;


    // Build the new filters manually, not relying on filters state directly
    const updatedFilters = {
      ...filters,
      [type]: value, // empty string allowed
    };

    setFilters(updatedFilters);

    const allEmpty = Object.values(updatedFilters).every(val => val.trim() === "");
    if (allEmpty) {
      formik.setFieldValue("bill_wise_opening_balance_details", originalDataRef.current);
      return;
    }
      setFilters(updatedFilters);
      const filteredData = originalDataRef.current.filter(item => {
        return Object.keys(updatedFilters).every((key) => {
          const filterVal = updatedFilters[key].toString().toLowerCase();
          if (!filterVal) return true;

        if (key === "transaction_type") {
            // Special handling for transaction_type
            const transaction = billWiseTransactionType.find(t => t.value === item[key]);
            const transactionLabel = transaction?.label?.toLowerCase() || '';
            return transactionLabel.includes(filterVal);
        } else {
            const itemVal = (item[key] || '').toString().toLowerCase();
            return itemVal.includes(filterVal);
        }
        });
      });

      formik.setFieldValue("bill_wise_opening_balance_details", filteredData);
  }

      const handleRequireOpeningBalance = (row) => {
        const hasNonEmptyData = Object.entries(row)
        .filter(([key]) => key !== "is_editable")
        .some(([, value]) => value !== "" && value !== null);
        return hasNonEmptyData
      }

    const handleOpenCessRateModal = (id) => {
        setIsAddCessRate(true);
        setCessRateId(id);
    }

    return (
        <>
            <CustomHelmet
                title={id ? "Update Ledger" : "Add Ledger"}
             />
            {loader ? (
                <Loader />
            ) : ledger?.status === 404 && !window.location.pathname.includes("/create") ? (
                <Error404Page />
            ) : (
                <form ref={formRef} onSubmit={formik.handleSubmit}  onInput={handleInput} >
                    <div className="d-flex flex-column justify-content-between h-100 custom-min-height">
                        <div>
                            <Container fluid className="p-0">
                                <div className="content-wrapper py-6 px-lg-9 px-6 overflow-visible">
                                    <div>
                                        <div>
                                            <div className="ledger-transaction">
                                                <Row className="align-items-center">
                                                    <Col
                                                        xxl={2}
                                                        xl={3}
                                                        lg={4}
                                                        sm={6}
                                                        className="mb-sm-0 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                ref={inputRef}
                                                                className="floating-label-input capitalize pe-6"
                                                                type="text"
                                                                placeholder=""
                                                                required
                                                                name="name"
                                                                value={formik.values?.name}
                                                                onChange={e =>
                                                                    changeLedgerName(e.target.value)
                                                                }
                                                                onBlur={handleBlurItem}
                                                                onFocus={handleFocusItem}
                                                                maxLength={100}
                                                                disabled={isDefault}
                                                                autoFocus={true}
                                                            />
                                                            <Form.Label className="required">
                                                                Ledger Name
                                                            </Form.Label>
                                                            {isDefault ? (
                                                                " "
                                                            ) : (
                                                                <p
                                                                    className={`position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea`}
                                                                >
                                                                    {isFocusedItem
                                                                        ? 100 - nameCount || 0
                                                                        : ""}
                                                                </p>
                                                            )}
                                                        </Form.Group>
                                                    </Col>
                                                    <Col
                                                        xxl={2}
                                                        xl={3}
                                                        lg={4}
                                                        sm={6}
                                                        className="mb-0 form-group-select"
                                                    >
                                                        <Form.Group>
                                                            <div className="input-group flex-nowrap">
                                                                <div className="position-relative h-40px w-100 pe-36px radius-r-0 focus-shadow">
                                                                    <ReactSelect
                                                                        options={
                                                                            id
                                                                                ? groupLedgerList
                                                                                : ledgerDetailOptions
                                                                        }
                                                                        placeholder={"Group"}
                                                                        required={true}
                                                                        name="group_id"
                                                                        value={
                                                                            formik.values?.group_id
                                                                        }
                                                                        onChange={e =>
                                                                            handleGroupChange(e)
                                                                        }
                                                                        className="h-40px"
                                                                        portal={true}
                                                                        // isDisabled={isDefault}
                                                                    />
                                                                </div>
                                                                <button
                                                                    type="button"
                                                                    className="input-group-text custom-group-text"
                                                                    onClick={openItemCatModel}
                                                                    // disabled={isDefault}
                                                                >
                                                                    <i className="fas fa-plus text-gray-900"></i>
                                                                </button>
                                                            </div>
                                                        </Form.Group>
                                                    </Col>
                                                </Row>
                                                <div className="form-check form-check-custom mt-4 w-100">
                                                    <input
                                                        className="form-check-input me-2"
                                                        type="checkbox"
                                                        name="is_use_in_other_companies"
                                                        id="isUseInOtherCompanies"
                                                        checked={formik.values.is_use_in_other_companies == 1 ? true : false}
                                                        onChange={e =>
                                                            formik.setFieldValue(
                                                                "is_use_in_other_companies",
                                                                e.target.checked ? 1 : 0
                                                            )
                                                        }
                                                    />
                                                    <label
                                                        htmlFor="isUseInOtherCompanies"
                                                        className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                                    >
                                                        Use in other Companies
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {formik.values?.parent_action !== "Supplier" &&
                                    formik.values?.parent_action !== "Customers" &&
                                    formik.values?.group_id &&
                                    fields?.length > 0 && (
                                        <>
                                            <div className="content-wrapper py-6 px-lg-9 px-6 overflow-visible mt-8">
                                                <Row className="align-items-center">
                                                    {fields.map((field, index) =>
                                                        renderLedgerDetail({
                                                            id,
                                                            ledgerGroupType,
                                                            formik,
                                                            AccountTypeOptions,
                                                            gstOptions,
                                                            gstCessOptions,
                                                            roundOffOption,
                                                            Country,
                                                            State,
                                                            City,
                                                            brokerageOption,
                                                            ledgerEntityType,
                                                            changeIfscCode,
                                                            openLocationOfAssetModel,
                                                            isShowProfitRatio,
                                                            handleHSNCode,
                                                            error,
                                                            company,
                                                            isFocused,
                                                            setIsFocused,
                                                            descriptionCount,
                                                            setDescriptionCount,
                                                            handleChangeTaxType,
                                                            changePanDetail,
                                                            handleOpenStockModel,
                                                            openingItemStock,
                                                            itemStockList,
                                                            handleDeleteStockModel,
                                                            multiDate,
                                                            handleDateChange,
                                                            setSwiftCodeShow,
                                                            handleOpenCessRateModal,
                                                        })
                                                    )}
                                                </Row>
                                            </div>
                                        </>
                                    )}

                                {(formik.values?.parent_action === "Supplier" ||
                                    formik.values?.parent_action === "Customers") && (
                                    <>
                                            <div className="content-wrapper py-6 px-lg-9 px-6 overflow-visible mt-8">
                                            <Row>
                                                <Col sm={12}>
                                                    <h5 className="mb-4 text-primary">
                                                        Party Details
                                                    </h5>
                                                </Col>
                                                <Col xxl={2} xl={3} lg={4} sm={6} className="mb-5">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input"
                                                            type="text"
                                                            placeholder=""
                                                            name="party_details.gstin"
                                                            value={
                                                                formik.values?.party_details
                                                                    ?.gstin ?? null
                                                            }
                                                            onChange={changeGstNumber}
                                                        />
                                                        <Form.Label>GSTIN</Form.Label>
                                                    </Form.Group>
                                                    {GSTError && (
                                                        <span className="text-danger">
                                                            {GSTError}
                                                        </span>
                                                    )}
                                                </Col>
                                            </Row>

                                            <Row>
                                                <Col sm={12}>
                                                    <h5 className="mb-4 text-primary">
                                                        Billing Address
                                                    </h5>
                                                </Col>
                                                <Col xxl={4} sm={6} className="mb-5">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input capitalize"
                                                            type="text"
                                                            placeholder=""
                                                            name="party_details.billing_address.address_1"
                                                            value={
                                                                formik.values?.party_details
                                                                    ?.billing_address?.address_1
                                                            }
                                                            onChange={formik.handleChange}
                                                        />
                                                        <Form.Label>Address Line 1</Form.Label>
                                                    </Form.Group>
                                                </Col>
                                                <Col xxl={4} sm={6} className="mb-5">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input capitalize"
                                                            type="text"
                                                            placeholder=""
                                                            name="party_details.billing_address.address_2"
                                                            value={
                                                                formik.values?.party_details
                                                                    ?.billing_address?.address_2
                                                            }
                                                            onChange={formik.handleChange}
                                                        />
                                                        <Form.Label>Address Line 2</Form.Label>
                                                    </Form.Group>
                                                </Col>
                                                <Col sm={12} className="p-0">
                                                    <Row className="m-0">
                                                        <Col
                                                            xxl={2}
                                                            xl={3}
                                                            lg={4}
                                                            sm={6}
                                                            className="mb-5"
                                                        >
                                                            <div className="input-group flex-nowrap">
                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                    <Country
                                                                        required={
                                                                            company?.company
                                                                                ?.is_gst_applicable
                                                                                ? true
                                                                                : false
                                                                        }
                                                                        formik={formik}
                                                                        name="party_details.billing_address.country_id"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </Col>
                                                        <Col
                                                            xxl={2}
                                                            xl={3}
                                                            lg={4}
                                                            sm={6}
                                                            className="mb-5"
                                                        >
                                                            <div className="input-group flex-nowrap">
                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                    <State
                                                                        required={
                                                                            company?.company
                                                                                ?.is_gst_applicable
                                                                                ? true
                                                                                : false
                                                                        }
                                                                        formik={formik}
                                                                        name="party_details.billing_address.state_id"
                                                                        city_name="party_details.billing_address.city_id"
                                                                        companyState={companyState}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </Col>
                                                        <Col
                                                            xxl={2}
                                                            xl={3}
                                                            lg={4}
                                                            sm={6}
                                                            className="mb-5"
                                                        >
                                                            <div className="input-group flex-nowrap">
                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                    <City
                                                                        formik={formik}
                                                                        name="party_details.billing_address.city_id"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </Col>
                                                        <Col
                                                            xxl={2}
                                                            xl={3}
                                                            lg={4}
                                                            sm={6}
                                                            className="mb-5"
                                                        >
                                                            <Form.Group className="position-relative form-floating-group">
                                                                <FormInput
                                                                    className="floating-label-input"
                                                                    type="text"
                                                                    placeholder=""
                                                                    name="party_details.billing_address.pin_code"
                                                                    value={
                                                                        formik.values?.party_details
                                                                            ?.billing_address
                                                                            ?.pin_code
                                                                    }
                                                                    maxLength={6}
                                                                    minLength={6}
                                                                    onChange={formik.handleChange}
                                                                />
                                                                <Form.Label>Pincode</Form.Label>
                                                            </Form.Group>
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col sm={12}>
                                                    <Form.Group>
                                                        <div className="mb-4">
                                                            <div className="d-flex">
                                                                <h5 className="mb-1 pe-2 text-primary">
                                                                    Shipping Address
                                                                </h5>
                                                            </div>
                                                            <div className="form-check mb-0">
                                                                <input
                                                                    type="checkbox"
                                                                    id="dispatchAddress"
                                                                    className="form-check-input"
                                                                    value={
                                                                        formik.values?.party_details
                                                                            ?.same_as_billing_address
                                                                    }
                                                                    checked={
                                                                        formik.values?.party_details
                                                                            ?.same_as_billing_address ===
                                                                        true
                                                                    }
                                                                    onChange={e =>
                                                                        handleSameAsBill(
                                                                            e,
                                                                            "shipping"
                                                                        )
                                                                    }
                                                                />
                                                                <label
                                                                    title=""
                                                                        htmlFor="dispatchAddress"
                                                                    className="form-check-label"
                                                                >
                                                                    Same as Billing Address
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </Form.Group>
                                                </Col>
                                            </Row>

                                            {!formik.values?.party_details
                                                ?.same_as_billing_address === true && (
                                                <>
                                                <Row className="justify-content-start">
                                                    <Col
                                                        xxl={2}
                                                        xl={3}
                                                        lg={4}
                                                        sm={6}
                                                        className="px-3 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input"
                                                                type="text"
                                                                placeholder=""
                                                                name="party_details.shipping_address.address_name"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.address_name ?? null
                                                                }
                                                                onChange={formik.handleChange}
                                                                disabled={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.party_name_same_as_address_name
                                                                }
                                                            />
                                                            <Form.Label>Address Name</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col>
                                                        <div className="form-check mt-2">
                                                            <input
                                                                type="checkbox"
                                                                id="address_name"
                                                                className="form-check-input"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.party_name_same_as_address_name
                                                                }
                                                                checked={
                                                                    Boolean(
                                                                        formik.values.party_details
                                                                            ?.shipping_address
                                                                            ?.party_name_same_as_address_name
                                                                    ) == true
                                                                }
                                                                onChange={handleSameAsAddressName}
                                                            />
                                                            <label
                                                                title=""
                                                                htmlFor="address_name"
                                                                className="form-check-label"
                                                            >
                                                                Same as Party Name
                                                            </label>
                                                        </div>
                                                    </Col>
                                                </Row>
                                                    <Row className="justify-content-start">
                                                        <Col sm={12} className="p-0">
                                                            <div>
                                                                <Row className="m-0">
                                                                    {company?.company
                                                                        ?.is_gst_applicable ? (
                                                                        <Col
                                                                            xxl={2}
                                                                            xl={3}
                                                                            lg={4}
                                                                            sm={6}
                                                                            className="mb-5"
                                                                        >
                                                                            <Form.Group className="position-relative form-floating-group">
                                                                                <FormInput
                                                                                    className="floating-label-input"
                                                                                    type="text"
                                                                                    placeholder=""
                                                                                    name="party_details.shipping_address.shipping_gstin"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .party_details
                                                                                            ?.shipping_address
                                                                                            ?.shipping_gstin
                                                                                    }
                                                                                    onChange={
                                                                                        changeShippingGstNumber
                                                                                    }
                                                                                    minLength="15"
                                                                                    maxLength="15"
                                                                                />
                                                                                <Form.Label>
                                                                                    GSTIN
                                                                                </Form.Label>
                                                                            </Form.Group>
                                                                            {GSTError2 && (
                                                                                <span className="text-danger">
                                                                                    {GSTError2}
                                                                                </span>
                                                                            )}
                                                                        </Col>
                                                                    ) : null}
                                                                    <Col
                                                                        xxl={2}
                                                                        xl={3}
                                                                        lg={4}
                                                                        sm={6}
                                                                        className="mb-5"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput
                                                                                className="floating-label-input capitalize"
                                                                                type="text"
                                                                                placeholder=""
                                                                                name="party_details.shipping_address.shipping_name"
                                                                                value={
                                                                                    formik.values
                                                                                        .party_details
                                                                                        ?.shipping_address
                                                                                        ?.shipping_name
                                                                                }
                                                                                onChange={handleChangeAddressName}
                                                                            />
                                                                            <Form.Label>
                                                                                Party Name
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                    </Col>
                                                                </Row>
                                                            </div>
                                                        </Col>
                                                        <Col xxl={4} sm={6} className="mb-5">
                                                            <Form.Group className="position-relative form-floating-group">
                                                                <FormInput
                                                                    className="floating-label-input capitalize"
                                                                    type="text"
                                                                    placeholder=""
                                                                    name="party_details.shipping_address.address_1"
                                                                    value={
                                                                        formik.values?.party_details
                                                                            ?.shipping_address
                                                                            ?.address_1
                                                                    }
                                                                    onChange={formik.handleChange}
                                                                />
                                                                <Form.Label>
                                                                    Address Line 1
                                                                </Form.Label>
                                                            </Form.Group>
                                                        </Col>
                                                        <Col xxl={4} sm={6} className="mb-5">
                                                            <Form.Group className="position-relative form-floating-group">
                                                                <FormInput
                                                                    className="floating-label-input capitalize"
                                                                    type="text"
                                                                    placeholder=""
                                                                    name="party_details.shipping_address.address_2"
                                                                    value={
                                                                        formik.values?.party_details
                                                                            ?.shipping_address
                                                                            ?.address_2
                                                                    }
                                                                    onChange={formik.handleChange}
                                                                />
                                                                <Form.Label>
                                                                    Address Line 2
                                                                </Form.Label>
                                                            </Form.Group>
                                                        </Col>
                                                        <Col sm={12} className="p-0">
                                                            <Row className="m-0">
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <div className="input-group flex-nowrap">
                                                                        <div className="position-relative h-40px w-100 focus-shadow">
                                                                            <Country2
                                                                                formik={formik}
                                                                                required={
                                                                                    company?.company
                                                                                        ?.is_gst_applicable
                                                                                        ? true
                                                                                        : false
                                                                                }
                                                                                name="party_details.shipping_address.country_id"
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <div className="input-group flex-nowrap">
                                                                        <div className="position-relative h-40px w-100 focus-shadow">
                                                                            <State2
                                                                                formik={formik}
                                                                                required={
                                                                                    company?.company
                                                                                        ?.is_gst_applicable
                                                                                        ? true
                                                                                        : false
                                                                                }
                                                                                name="party_details.shipping_address.state_id"
                                                                                city_name="party_details.shipping_address.city_id"
                                                                                companyState={
                                                                                    companyState
                                                                                }
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <div className="input-group flex-nowrap">
                                                                        <div className="position-relative h-40px w-100 focus-shadow">
                                                                            <City2
                                                                                formik={formik}
                                                                                name="party_details.shipping_address.city_id"
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group className="position-relative form-floating-group">
                                                                        <FormInput
                                                                            className="floating-label-input"
                                                                            type="text"
                                                                            placeholder=""
                                                                            name="party_details.shipping_address.pin_code"
                                                                            value={
                                                                                formik.values
                                                                                    ?.party_details
                                                                                    ?.shipping_address
                                                                                    ?.pin_code
                                                                            }
                                                                            onChange={
                                                                                formik.handleChange
                                                                            }
                                                                            maxLength={6}
                                                                            minLength={6}
                                                                        />
                                                                        <Form.Label>
                                                                            Pincode
                                                                        </Form.Label>
                                                                    </Form.Group>
                                                                </Col>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                    {id ?
                                                    <button
                                                        type="button"
                                                        className="btn-sm btn-icon btn-icon-primary mb-5 add-item-btn"
                                                        onClick={handleOpenShippingAddressModal}
                                                    >
                                                        Manage Addresses
                                                    </button>
                                                     : ""}
                                                </>
                                            )}

                                            <Row>
                                                <Col sm={12}>
                                                    <h5 className="mb-4 text-primary">
                                                        Contact Details
                                                    </h5>
                                                </Col>
                                                <Col xxl={2} xl={3} lg={4} sm={6} className="mb-5">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input capitalize"
                                                            type="text"
                                                            placeholder=""
                                                            name="party_details.contact_person_name"
                                                            value={
                                                                formik.values?.party_details
                                                                    ?.contact_person_name
                                                            }
                                                            onChange={formik.handleChange}
                                                        />
                                                        <Form.Label>Contact Person Name</Form.Label>
                                                    </Form.Group>
                                                </Col>
                                                <Col xxl={2} xl={3} lg={4} sm={6} className="mb-5">
                                                    <PhoneInput
                                                        country={"in"}
                                                        value={
                                                            formik.values?.party_details
                                                                ?.contact_person_phone_input_1 || "+91"
                                                        }
                                                        onChange={(phone, code) =>
                                                            handlePhoneChange(phone, code)
                                                        }
                                                        countryCodeEditable={false}
                                                        containerClass="w-100"
                                                        inputClass="w-100 h-40px fw-500"
                                                    />
                                                </Col>
                                                <Col xxl={2} xl={3} lg={4} sm={6} className="mb-5">
                                                    <PhoneInput
                                                        country={"in"}
                                                        value={
                                                            formik.values?.party_details
                                                                ?.contact_person_phone_input_2 || "+91"
                                                        }
                                                        countryCodeEditable={false}
                                                        onChange={(phone, code) =>
                                                            handlePhoneChange2(phone, code)
                                                        }
                                                        containerClass="w-100"
                                                        inputClass="w-100 h-40px fw-500"
                                                    />
                                                </Col>
                                                <Col xxl={2} xl={3} lg={4} sm={6} className="mb-5">
                                                    <Form.Group className="position-relative form-floating-group">
                                                        <FormInput
                                                            className="floating-label-input"
                                                            type="email"
                                                            placeholder=""
                                                            name="party_details.contact_person_email"
                                                            value={
                                                                formik.values?.party_details
                                                                    ?.contact_person_email
                                                            }
                                                            onChange={formik.handleChange}
                                                        />
                                                        <Form.Label>Email</Form.Label>
                                                        <div className="input-group-text top-0 right-0 position-absolute custom-group-text">
                                                            <i className="fas fa-envelope text-gray-900"></i>
                                                        </div>
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </div>
                                            <div className="content-wrapper py-6 px-lg-9 px-6 overflow-visible mt-8">
                                            <div
                                                className="accordion"
                                                id="accordionPanelsStayOpenExample"
                                            >
                                                <div className="accordion-item">
                                                    <h2 className="accordion-header">
                                                        <button
                                                            className="accordion-button fw-bolder"
                                                            type="button"
                                                            data-bs-toggle="collapse"
                                                            data-bs-target="#panelsStayOpen-collapseOne"
                                                            aria-expanded="false"
                                                            aria-controls="panelsStayOpen-collapseOne"
                                                        >
                                                            Tax Details
                                                        </button>
                                                    </h2>
                                                    <div
                                                        id="panelsStayOpen-collapseOne"
                                                        className="accordion-collapse collapse show"
                                                    >
                                                        <div className="accordion-body mt-5">
                                                            <Row>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group className="position-relative form-floating-group">
                                                                        <FormInput
                                                                            className="floating-label-input"
                                                                            type="text"
                                                                            placeholder=""
                                                                            name="tax_details.pan"
                                                                            value={
                                                                                formik.values
                                                                                    .tax_details
                                                                                    ?.pan || ""
                                                                            }
                                                                            onChange={
                                                                                changePanDetail
                                                                            }
                                                                            minLength="10"
                                                                            maxLength="10"
                                                                        />
                                                                        <Form.Label>PAN</Form.Label>
                                                                    </Form.Group>
                                                                    {formik?.errors?.tax_details
                                                                        ?.pan &&
                                                                        formik?.touched?.tax_details
                                                                            ?.pan && (
                                                                            <div className="text-danger position-relative ps-1">
                                                                                {
                                                                                    formik?.errors
                                                                                        ?.tax_details
                                                                                        ?.pan
                                                                                }
                                                                            </div>
                                                                        )}
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group>
                                                                        <div className="input-group flex-nowrap">
                                                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                                                <ReactSelect
                                                                                    name="tax_details
                                                                                    .type_of_entity"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .tax_details
                                                                                            ?.type_of_entity
                                                                                    }
                                                                                    onChange={e =>{
                                                                                        setisFieldsChanges(true);
                                                                                        formik.setFieldValue(
                                                                                            "tax_details.type_of_entity",
                                                                                            e.value
                                                                                        );
                                                                                    }
                                                                                    }
                                                                                    placeholder="Type of Entity"
                                                                                    options={
                                                                                        ledgerEntityType
                                                                                    }
                                                                                    isCreatable={
                                                                                        false
                                                                                    }
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </Form.Group>
                                                                </Col>
                                                                {formik.values?.tax_details
                                                                    ?.type_of_entity_character ==
                                                                "C" ? (
                                                                    <Col
                                                                        xxl={2}
                                                                        xl={3}
                                                                        lg={4}
                                                                        sm={6}
                                                                        className="mb-5"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput
                                                                                className="floating-label-input"
                                                                                type="text"
                                                                                placeholder=""
                                                                                name="tax_details.cin_number"
                                                                                value={
                                                                                    formik.values
                                                                                        .tax_details
                                                                                        ?.cin_number
                                                                                }
                                                                                onChange={
                                                                                    changeCinDetail
                                                                                }
                                                                            />
                                                                            <Form.Label>
                                                                                CIN
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                    </Col>
                                                                ) : null}
                                                                    <Col
                                                                        xxl={2}
                                                                        xl={3}
                                                                        lg={4}
                                                                        sm={6}
                                                                        className="mb-5 w-full"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                                    <ReactSelect
                                                                                        name="tax_details
                                                                            .gst_registration_type"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .tax_details
                                                                                                ?.gst_registration_type
                                                                                        }
                                                                                        required={
                                                                                            true
                                                                                        }
                                                                                        onChange={e => {
                                                                                            setisFieldsChanges(true);
                                                                                            formik.setFieldValue(
                                                                                                "tax_details.gst_registration_type",
                                                                                                e.value
                                                                                            );
                                                                                        }
                                                                                        }
                                                                                        placeholder="GST Registration Type"
                                                                                        options={
                                                                                            gstRegistrationType
                                                                                        }
                                                                                        isCreatable={
                                                                                            false
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                {company?.company
                                                                    ?.is_gst_applicable &&
                                                                formik.values?.parent_action ===
                                                                    "Supplier" ? (
                                                                    <Col
                                                                        xxl={2}
                                                                        xl={3}
                                                                        lg={4}
                                                                        sm={6}
                                                                        className="mb-5"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput
                                                                                className="floating-label-input"
                                                                                type="text"
                                                                                placeholder=""
                                                                                name="tax_details.gst_return_status"
                                                                                value={
                                                                                    formik.values
                                                                                        .tax_details
                                                                                        ?.gst_return_status
                                                                                }
                                                                                onChange={
                                                                                    formik.handleChange
                                                                                }
                                                                            />
                                                                            <Form.Label>
                                                                                GST Return Status
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                    </Col>
                                                                ) : null}
                                                                {formik.values?.parent_action ===
                                                                    "Supplier" &&
                                                                company?.company
                                                                    ?.is_tds_applicable ? (
                                                                    <Col
                                                                        xxl={2}
                                                                        xl={3}
                                                                        lg={4}
                                                                        sm={6}
                                                                        className="mb-5"
                                                                    >
                                                                        <label className="form-label fs-6 fw-bolder mb-0">
                                                                            TDS Applicable ?
                                                                        </label>
                                                                        <div className="ms-auto d-flex">
                                                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                                                <label
                                                                                    className="form-label mb-0"
                                                                                    htmlFor="Yes"
                                                                                >
                                                                                    Yes
                                                                                </label>
                                                                                <input
                                                                                    className="form-check-input"
                                                                                    id="Yes"
                                                                                    name="tax_details.is_tds_applicable"
                                                                                    value={true}
                                                                                    checked={
                                                                                        formik
                                                                                            .values
                                                                                            .tax_details
                                                                                            ?.is_tds_applicable ===
                                                                                        true
                                                                                    }
                                                                                    onChange={() =>
                                                                                        formik.setFieldValue(
                                                                                            "tax_details.is_tds_applicable",
                                                                                            true
                                                                                        )
                                                                                    }
                                                                                    type="radio"
                                                                                />
                                                                            </div>
                                                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                                                <label
                                                                                    className="form-label mb-0"
                                                                                    htmlFor="No"
                                                                                >
                                                                                    No
                                                                                </label>
                                                                                <input
                                                                                    className="form-check-input"
                                                                                    id="No"
                                                                                    name="tax_details.is_tds_applicable"
                                                                                    value={false}
                                                                                    checked={
                                                                                        formik
                                                                                            .values
                                                                                            .tax_details
                                                                                            ?.is_tds_applicable ===
                                                                                        false
                                                                                    }
                                                                                    onChange={() =>
                                                                                        formik.setFieldValue(
                                                                                            "tax_details.is_tds_applicable",
                                                                                            false
                                                                                        )
                                                                                    }
                                                                                    type="radio"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </Col>
                                                                ) : null}
                                                            </Row>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                            <div className="content-wrapper py-6 px-lg-9 px-6 overflow-visible mt-8">
                                            <div
                                                className="accordion"
                                                id="accordionPanelsStayOpenExample"
                                            >
                                                <div className="accordion-item">
                                                    <h2 className="accordion-header">
                                                        <button
                                                            className="accordion-button collapsed fw-bolder"
                                                            type="button"
                                                            data-bs-toggle="collapse"
                                                            data-bs-target="#panelsStayOpen-collapseTwo"
                                                            aria-expanded="false"
                                                            aria-controls="panelsStayOpen-collapseTwo"
                                                        >
                                                            Other Details
                                                        </button>
                                                    </h2>
                                                    <div
                                                        id="panelsStayOpen-collapseTwo"
                                                        className="accordion-collapse collapse show"
                                                    >
                                                        <div className="accordion-body mt-5">
                                                            <Row className="align-items-center">
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group className="position-relative form-floating-group h-40px">
                                                                        <FormInput
                                                                            className="floating-label-input h-100"
                                                                            type="number"
                                                                            min={1}
                                                                            placeholder=""
                                                                            name="other_details.credit_period"
                                                                            value={
                                                                                formik.values
                                                                                    .other_details
                                                                                    ?.credit_period
                                                                            }
                                                                            onChange={
                                                                                formik.handleChange
                                                                            }
                                                                        />
                                                                        <Form.Label>
                                                                            Credit Period
                                                                        </Form.Label>
                                                                        <div className="w-fit-content bg-light position-absolute credit-dropdown focus-shadow input-group d-block">
                                                                            <ReactSelect
                                                                                defaultValue={1}
                                                                                options={[
                                                                                    {
                                                                                        label: "Month",
                                                                                        value: 1,
                                                                                    },
                                                                                    {
                                                                                        label: "Day",
                                                                                        value: 2,
                                                                                    },
                                                                                ]}
                                                                                name="other_details.credit_period_type"
                                                                                value={
                                                                                    formik.values
                                                                                        .other_details
                                                                                        ?.credit_period_type
                                                                                }
                                                                                onChange={e => {
                                                                                    setisFieldsChanges(true);
                                                                                    formik.setFieldValue(
                                                                                        "other_details.credit_period_type",
                                                                                        e.value
                                                                                    );
                                                                                }
                                                                                }
                                                                                placeholder=""
                                                                                isCreatable={false}
                                                                                showborder={false}
                                                                                showbg={true}
                                                                                height="32px"
                                                                            />
                                                                        </div>
                                                                    </Form.Group>
                                                                </Col>
                                                            </Row>
                                                            <Row>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5 form-group-select"
                                                                >
                                                                    <Form.Group>
                                                                        <div className="input-group flex-nowrap">
                                                                            <div className="position-relative h-40px w-100 pe-36px focus-shadow">
                                                                                <ReactSelect
                                                                                    customLabel="party"
                                                                                    options={
                                                                                        brokerOptions
                                                                                    }
                                                                                    name="other_details.broker"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.broker
                                                                                    }
                                                                                    onChange={
                                                                                        changeBrokerDetail
                                                                                    }
                                                                                    placeholder={
                                                                                        "Broker Name"
                                                                                    }
                                                                                    defaultLabel={
                                                                                        "Select Broker"
                                                                                    }
                                                                                    islabel={true}
                                                                                    portal={true}
                                                                                    radius={true}
                                                                                    className="h-40px"
                                                                                />
                                                                            </div>
                                                                            <button
                                                                                type="button"
                                                                                className="input-group-text custom-group-text"
                                                                                onClick={
                                                                                    handleBrokerModel
                                                                                }
                                                                            >
                                                                                <i className="fas fa-plus text-gray-900"></i>
                                                                            </button>
                                                                        </div>
                                                                    </Form.Group>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group>
                                                                        <div className="input-group flex-nowrap">
                                                                            <div className="position-relative h-40px w-100 form-floating-group">
                                                                                <FormInput
                                                                                    className="position-relative floating-label-input h-100"
                                                                                    type="number"
                                                                                    step="0.01"
                                                                                    max={100}
                                                                                    placeholder=""
                                                                                    name="other_details.brokerage_percentage"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.brokerage_percentage
                                                                                    }
                                                                                    onChange={
                                                                                        formik.handleChange
                                                                                    }
                                                                                />
                                                                                <Form.Label>
                                                                                    Brokerage
                                                                                    Percentage
                                                                                </Form.Label>
                                                                            </div>
                                                                            <div className="input-group-text custom-group-text">
                                                                                <i className="fas fa-percentage text-gray-900"></i>
                                                                            </div>
                                                                        </div>
                                                                    </Form.Group>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5 form-group-select"
                                                                >
                                                                    <Form.Group>
                                                                        <div className="input-group flex-nowrap">
                                                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                                                <ReactSelect
                                                                                    options={
                                                                                        brokerageOption
                                                                                    }
                                                                                    placeholder={
                                                                                        "Brokerage On Value"
                                                                                    }
                                                                                    name="other_details.brokerage_on_value"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.brokerage_on_value
                                                                                    }
                                                                                    onChange={e => {
                                                                                        setisFieldsChanges(true);
                                                                                        formik.setFieldValue(
                                                                                            "other_details.brokerage_on_value",
                                                                                            e.value
                                                                                        );
                                                                                    }
                                                                                    }
                                                                                    islabel={true}
                                                                                    portal={true}
                                                                                    className="h-40px"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </Form.Group>
                                                                </Col>
                                                            </Row>
                                                            <Row>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5 form-group-select"
                                                                >
                                                                    <Form.Group>
                                                                        <div className="input-group flex-nowrap">
                                                                            <div className="position-relative h-40px w-100 pe-36px focus-shadow">
                                                                                <ReactSelect
                                                                                    options={
                                                                                        transportOptions
                                                                                    }
                                                                                    customLabel="party"
                                                                                    placeholder={
                                                                                        "Transporter Name"
                                                                                    }
                                                                                    defaultLabel={
                                                                                        "Select Transporter"
                                                                                    }
                                                                                    name="other_details.transporter"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.transporter
                                                                                    }
                                                                                    onChange={e => {
                                                                                        setisFieldsChanges(true);
                                                                                        formik.setFieldValue(
                                                                                            "other_details.transporter",
                                                                                            e.value
                                                                                        );
                                                                                    }
                                                                                    }
                                                                                    islabel={true}
                                                                                    portal={true}
                                                                                    radius={true}
                                                                                    className="h-40px"
                                                                                    handleOpen={
                                                                                        handleOpenTransportModel
                                                                                    }
                                                                                />
                                                                            </div>
                                                                            <button
                                                                                type="button"
                                                                                className="input-group-text custom-group-text"
                                                                                onClick={() =>
                                                                                    handleOpenTransportModel(
                                                                                        ""
                                                                                    )
                                                                                }
                                                                            >
                                                                                <i className="fas fa-plus text-gray-900"></i>
                                                                            </button>
                                                                        </div>
                                                                    </Form.Group>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <Form.Group className="position-relative form-floating-group">
                                                                        <OverlayTrigger
                                                                            placement="bottom"
                                                                            overlay={
                                                                                <Tooltip id="file-upload-tooltip">
                                                                                    Maximum file
                                                                                    size is 2 MB.
                                                                                </Tooltip>
                                                                            }
                                                                        >
                                                                            <FormInput
                                                                                className="floating-label-input file-upload-validate"
                                                                                data-bs-toggle="tooltip"
                                                                                data-bs-placement="bottom"
                                                                                title=""
                                                                                name="sale_document"
                                                                                type="file"
                                                                                data-bs-original-title="Maximum file size is 2 MB."
                                                                                aria-label="Maximum file size is 2 MB."
                                                                                // onChange={(
                                                                                //     e
                                                                                // ) =>
                                                                                //     setCharges(
                                                                                //         {
                                                                                //             ...charges,
                                                                                //             upload_document:
                                                                                //                 e
                                                                                //                     .currentTarget
                                                                                //                     .files[0],
                                                                                //         }
                                                                                //     )
                                                                                // }
                                                                                multiple
                                                                                onChange={
                                                                                    validateFiles
                                                                                }
                                                                                accept=".jpg,.jpeg,.png,.pdf,.xlsx,.docx"
                                                                            />
                                                                        </OverlayTrigger>
                                                                        <Form.Label
                                                                            className="upload-document"
                                                                            htmlFor="sale_document"
                                                                        >
                                                                            Upload Document
                                                                        </Form.Label>
                                                                    </Form.Group>
                                                                </Col>
                                                                <Col
                                                                    xxl={2}
                                                                    xl={3}
                                                                    lg={4}
                                                                    sm={6}
                                                                    className="mb-5"
                                                                >
                                                                    <DocumentModal
                                                                        medias={
                                                                            formik.values
                                                                                ?.other_details
                                                                                ?.upload_document
                                                                        }
                                                                        formik={formik}
                                                                    />
                                                                </Col>
                                                            </Row>
                                                            {formik.values?.parent_action !==
                                                            "Supplier" ? (
                                                                <Row className="align-items-center">
                                                                    <Col
                                                                        xxl={2}
                                                                        xl={3}
                                                                        lg={4}
                                                                        sm={6}
                                                                        className="mb-5"
                                                                    >
                                                                        <label
                                                                            htmlFor="itemType"
                                                                            className="form-label fs-6 fw-bolder text-gray-900 mb-1"
                                                                        >
                                                                            Allow Credit Limit
                                                                        </label>
                                                                        <div className="d-flex align-items-center mt-0">
                                                                            <div className="form-check mx-2">
                                                                                <label
                                                                                    className="form-label mb-0 text-gray-900"
                                                                                    htmlFor="limitYes"
                                                                                >
                                                                                    Yes
                                                                                </label>
                                                                                <input
                                                                                    className="form-check-input item-type valid-item-type"
                                                                                    id="limitYes"
                                                                                    name="other_details.allow_credit_limit"
                                                                                    type="radio"
                                                                                    checked={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.allow_credit_limit ===
                                                                                        1
                                                                                    }
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.allow_credit_limit
                                                                                    }
                                                                                    onChange={() =>
                                                                                        handleCreditLimitChange(
                                                                                            1
                                                                                        )
                                                                                    }
                                                                                />
                                                                            </div>
                                                                            <div className="form-check mx-2">
                                                                                <label
                                                                                    className="form-label mb-0 text-gray-900"
                                                                                    htmlFor="LimitNo"
                                                                                >
                                                                                    No
                                                                                </label>
                                                                                <input
                                                                                    className="form-check-input item-type valid-item-type"
                                                                                    id="LimitNo"
                                                                                    name="creditLimit"
                                                                                    type="radio"
                                                                                    defaultChecked
                                                                                    checked={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.allow_credit_limit ===
                                                                                        0
                                                                                    }
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.allow_credit_limit
                                                                                    }
                                                                                    onChange={() =>
                                                                                        handleCreditLimitChange(
                                                                                            0
                                                                                        )
                                                                                    }
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </Col>
                                                                    {formik.values?.other_details
                                                                        ?.allow_credit_limit ===
                                                                    1 ? (
                                                                        <>
                                                                            <Col
                                                                                xxl={2}
                                                                                xl={3}
                                                                                lg={4}
                                                                                sm={6}
                                                                                className="mb-5"
                                                                            >
                                                                                <Form.Group>
                                                                                    <div className="input-group flex-nowrap">
                                                                                        <div className="position-relative h-40px w-100 form-floating-group">
                                                                                            <Form.Control
                                                                                                className="position-relative floating-label-input"
                                                                                                type="number"
                                                                                                step="0.01"
                                                                                                style={{
                                                                                                    paddingLeft:
                                                                                                        "43px",
                                                                                                }}
                                                                                                placeholder=""
                                                                                                name="other_details.credit_limit"
                                                                                                value={
                                                                                                    formik
                                                                                                        .values
                                                                                                        .other_details
                                                                                                        ?.credit_limit ||
                                                                                                    ""
                                                                                                }
                                                                                                onChange={
                                                                                                    formik.handleChange
                                                                                                }
                                                                                            />
                                                                                            <Form.Label
                                                                                                style={{
                                                                                                    left: "43px",
                                                                                                }}
                                                                                            >
                                                                                                Credit
                                                                                                Limit
                                                                                                Amount
                                                                                            </Form.Label>
                                                                                        </div>
                                                                                        <button
                                                                                            className="input-group-text custom-group-text text-gray-900 fs-4"
                                                                                            style={{
                                                                                                cssText:
                                                                                                    "border-radius: 8px 0 0 8px !important; left: 0 !important; right: auto !important;",
                                                                                            }}
                                                                                            type="button"
                                                                                        >
                                                                                            {
                                                                                                company
                                                                                                    ?.company
                                                                                                    ?.currentCurrencySymbol
                                                                                            }
                                                                                        </button>
                                                                                    </div>
                                                                                </Form.Group>
                                                                            </Col>
                                                                            <Col
                                                                                xxl={2}
                                                                                xl={3}
                                                                                lg={4}
                                                                                sm={6}
                                                                                className="mb-5"
                                                                            >
                                                                                <div className="ms-auto d-flex">
                                                                                    <div className="form-check mx-2 mt-1 align-content-center">
                                                                                        <label
                                                                                            className="form-label mb-0"
                                                                                            htmlFor="warn"
                                                                                        >
                                                                                            Warn
                                                                                        </label>
                                                                                        <input
                                                                                            className="form-check-input"
                                                                                            id="warn"
                                                                                            name="group1"
                                                                                            type="radio"
                                                                                            defaultChecked
                                                                                            checked={
                                                                                                formik
                                                                                                    .values
                                                                                                    .other_details
                                                                                                    ?.credit_limit_action ===
                                                                                                1
                                                                                            }
                                                                                            value={
                                                                                                formik
                                                                                                    .values
                                                                                                    .other_details
                                                                                                    ?.credit_limit_action
                                                                                            }
                                                                                            onChange={() =>
                                                                                                changeCreditLimitAction(
                                                                                                    1
                                                                                                )
                                                                                            }
                                                                                        />
                                                                                    </div>
                                                                                    <div className="form-check mx-2 mt-1 align-content-center">
                                                                                        <label
                                                                                            className="form-label mb-0"
                                                                                            htmlFor="block"
                                                                                        >
                                                                                            Block
                                                                                        </label>
                                                                                        <input
                                                                                            className="form-check-input"
                                                                                            id="block"
                                                                                            name="group1"
                                                                                            type="radio"
                                                                                            checked={
                                                                                                formik
                                                                                                    .values
                                                                                                    .other_details
                                                                                                    ?.credit_limit_action ===
                                                                                                2
                                                                                            }
                                                                                            value={
                                                                                                formik
                                                                                                    .values
                                                                                                    .other_details
                                                                                                    ?.credit_limit_action
                                                                                            }
                                                                                            onChange={() =>
                                                                                                changeCreditLimitAction(
                                                                                                    2
                                                                                                )
                                                                                            }
                                                                                        />
                                                                                    </div>
                                                                                </div>
                                                                            </Col>
                                                                        </>
                                                                    ) : null}
                                                                </Row>
                                                            ) : null}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                            <div className="content-wrapper py-6 px-lg-9 px-6 overflow-visible mt-8">
                                            <div
                                                className="accordion"
                                                id="accordionPanelsStayOpenExample"
                                            >
                                                <div className="accordion-item">
                                                        <div className="d-flex flex-wrap justify-content-sm-start justify-content-between gap-2 gap-sm-14 align-items-center">
                                                    <h2 className="accordion-header">
                                                        <button
                                                            className="accordion-button collapsed fw-bolder"
                                                            type="button"
                                                            data-bs-toggle="collapse"
                                                            data-bs-target="#panelsStayOpen-collapseThree"
                                                            aria-expanded="false"
                                                            aria-controls="panelsStayOpen-collapseThree"
                                                        >
                                                            Opening Balance
                                                        </button>
                                                    </h2>
                                                            <div>
                                                                <div className="form-check form-switch form-check-custom">
                                                                    <label
                                                                        htmlFor="billWise"
                                                                        className="form-label fs-16 text-primary fw-bolder mb-0"
                                                                    >
                                                                        Bill Wise
                                                                    </label>
                                                                    <input
                                                                        className="form-check-input mx-2"
                                                                        type="checkbox"
                                                                        name="opening_balance_details.bill_wise"
                                                                        id="billWise"
                                                                        checked={formik.values.opening_balance_details?.bill_wise}
                                                                        onChange={handleCheckboxChange}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <div
                                                        id="panelsStayOpen-collapseThree"
                                                        className="accordion-collapse collapse show"
                                                    >
                                                        <div className="accordion-body mt-5">
                                                        {formik.values.opening_balance_details.bill_wise ? (
                                            <div className="bill-wise-content">
                                                <div className="d-flex justify-content-between align-items-center gap-4 mb-4">
                                                    <h5 style={{ marginBottom: "0" }}>Amount : {DrCrAmount} {isShowDrCr === "Cr" ? "Cr" : "Dr"}</h5>
                                                    {selectedRows.length > 0 && (
                                                        <button type="button" className="btn btn-sm btn-danger" onClick={handleBulkDelete}>
                                                            Bulk Delete
                                                        </button>
                                                    )}
                                                    <div className="d-flex justify-content-end align-items-center gap-5 ms-auto">
                                                        {/* <button type="button" className="btn btn-sm btn-primary">Total Balance:<span>₹{pendingAmount}</span></button> */}
                                                        <BillWiseModal formik={formik} billWiseTransactionType={billWiseTransactionType} />
                                                    </div>
                                                </div>
                                                <div className="overflow-auto">
                                                    <table className="bill-wise-table w-100 overflow-auto">
                                                        <thead>
                                                            <tr>
                                                                <th className="fw-5 text-nowrap"></th>
                                                                <th className="fw-5 text-nowrap">Voucher Number</th>
                                                                <th className="fw-5 text-nowrap">Voucher Date</th>
                                                                <th className="fw-5 text-nowrap">Due Date</th>
                                                                <th className="fw-5 text-nowrap">Transaction Type</th>
                                                                <th className="fw-5 text-nowrap">Total amount</th>
                                                                <th className="fw-5 text-nowrap">{formik.values.parent_action?.toLowerCase() === "supplier" ? "Paid Amount" : "Received Amount"}</th>
                                                                <th className="fw-5 text-nowrap">Pending Amount</th>
                                                                <th className="fw-5 text-nowrap"></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td className="fs-16 fw-5 text-center">
                                                                    <input className="form-check-input" type="checkbox" onChange={handleSelectAll}
                                                                        checked={selectedRows.length === formik.values.bill_wise_opening_balance_details?.filter((item) => item?.is_editable).length && selectedRows.length > 0} />
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                    <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e)=>customFilter(e, "voucher_number")} />
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                    <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e)=>customFilter(e, "voucher_date")} />
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                    <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e)=>customFilter(e, "due_date")} />
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                    <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e)=>customFilter(e, "transaction_type")} />
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                    <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e)=>customFilter(e, "total_amount")} />
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                   {formik.values.parent_action?.toLowerCase() === "supplier" ? <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e)=>customFilter(e, "paid_amount")} /> : <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e)=>customFilter(e, "received_amount")} />}
                                                                </td>
                                                                <td className="fs-16 fw-5">
                                                                    <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e)=>customFilter(e, "pending_amount")} />
                                                                </td>
                                                                <td></td>
                                                            </tr>
                                                            {formik.values.bill_wise_opening_balance_details.map((row, index) => (
                                                                <tr key={index}>
                                                                    <td className="fs-16 fw-5 text-center">
                                                                        <input className="form-check-input" disabled={!row?.is_editable} type="checkbox" checked={selectedRows.includes(index)}
                                                                            onChange={() => handleRowSelect(index)} />
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput className="floating-label-input" type="text" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.voucher_number`} placeholder="" value={row.voucher_number} onChange={handleBillWiseChange} required={handleRequireOpeningBalance(row)} onClick={(e) => e.target.click()} />
                                                                            <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>Voucher Number</Form.Label>
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <Datepicker onChange={(date) => handleChangeDueDate(date, index, "voucher")} disabled={!row?.is_editable}  id={index} value={row.voucher_date} placeholder={"Voucher Date"} required={handleRequireOpeningBalance(row)} />
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <Datepicker isDueDate={true} option={true} onChange={(date) => handleChangeDueDate(date, index, "due")} disabled={!row?.is_editable} value={row.due_date} placeholder={"Due Date"} required={handleRequireOpeningBalance(row)} />
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                                    <ReactSelect
                                                                                        options={billWiseTransactionType}
                                                                                        value={row.transaction_type}
                                                                                        onChange={(selected) => handleSelectChange(selected, index)}
                                                                                        placeholder="Transaction Type"
                                                                                        defaultLabel="Select Transaction Type"
                                                                                        className="react-select-container"
                                                                                        classNamePrefix="react-select"
                                                                                        required={handleRequireOpeningBalance(row)}
                                                                                        isDisabled={!row?.is_editable}
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput className="floating-label-input" type="number" placeholder="" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.total_amount`} value={row.total_amount} onChange={(e)=>handleBillWiseChange(e, index, "total_amount")} required={handleRequireOpeningBalance(row)} onClick={(e) => e.target.click()} />
                                                                            <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>Total Amount</Form.Label>
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group className="position-relative form-floating-group">                                                                           {formik.values.parent_action?.toLowerCase() === "supplier" ? (
                                                                                <FormInput className="floating-label-input" type="number" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.paid_amount`} placeholder="" value={row.paid_amount} required={handleRequireOpeningBalance(row)} onChange={(e)=>handleBillWiseChange(e, index, "paid_amount")} onClick={(e) => e.target.click()} />
                                                                            ) : (
                                                                                <FormInput className="floating-label-input" type="number" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.received_amount`} placeholder="" value={row.received_amount} required={handleRequireOpeningBalance(row)} onChange={(e)=>handleBillWiseChange(e, index, "received_amount")} onClick={(e) => e.target.click()} />
                                                                            )}
                                                                            <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>{formik.values.parent_action?.toLowerCase() === "supplier" ? "Paid Amount" : "Received Amount"}</Form.Label>
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput disabled className="floating-label-input" type="number" name={`bill_wise_opening_balance_details.${index}.pending_amount`} placeholder="" value={row.pending_amount} required={handleRequireOpeningBalance(row)} />
                                                                            <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>Pending Amount</Form.Label>
                                                                        </Form.Group>
                                                                    </td>
                                                                    <td className="fs-16 fw-5">
                                                                        {row?.is_editable && (
                                                                            <button className="btn p-0 d-flex" onClick={(e) => handleDeleteRow(index, e, row?.id)}>
                                                                                <img src={deleteSvg} alt="delete" />
                                                                            </button>
                                                                        )}
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                            <tr>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap">
                                                                    <button
                                                                        type="button"
                                                                        className="btn-sm btn-icon btn-icon-primary add-item-btn"
                                                                        onClick={handleAddRow}
                                                                    >
                                                                        Add New Bill
                                                                    </button>
                                                                </td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                            </tr>
                                                        </tbody>
                                                        <tfoot>
                                                            <tr>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap">Total amount</td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                                <td className="fs-16 fw-5 text-nowrap">{company?.company?.currentCurrencySymbol ||"₹"}{" "}{parseFloat(totalAmount)}</td>
                                                                <td className="fs-16 fw-5 text-nowrap">{company?.company?.currentCurrencySymbol ||"₹"}{" "}{parseFloat(receivedAmount)}</td>
                                                                <td className="fs-16 fw-5 text-nowrap">{company?.company?.currentCurrencySymbol ||"₹"}{" "}{parseFloat(pendingAmount)}</td>
                                                                <td className="fs-16 fw-5 text-nowrap"></td>
                                                            </tr>
                                                        </tfoot>
                                                    </table>
                                                </div>
                                            </div>
                                        ) : (
                                            <Row className="align-items-center">
                                            <Col
                                                xxl={2}
                                                xl={3}
                                                lg={4}
                                                sm={6}
                                                className="mb-sm-0 mb-5"
                                            >
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input"
                                                        type="text"
                                                        placeholder=""
                                                        name="opening_balance_details.opening_balance"
                                                        value={
                                                            formik.values
                                                                .opening_balance_details
                                                                ?.opening_balance
                                                        }
                                                        onChange={e => {
                                                            const value = e.target.value;
                                                                const regex = /^[0-9]*\.?[0-9]*$/;
                                                                    if (regex.test(Number(value))) {
                                                                        formik.setFieldValue(
                                                                            "opening_balance_details.opening_balance",
                                                                            value
                                                                        );
                                                                }
                                                        }}
                                                    />
                                                    <Form.Label>
                                                        Amount
                                                    </Form.Label>
                                                </Form.Group>
                                            </Col>
                                            <Col xxl={2} xl={3} lg={4} sm={6}>
                                                <label className="form-label fs-6 fw-bolder mb-0">
                                                    Opening Balance
                                                </label>
                                                <div className="ms-auto d-flex">
                                                    <div className="form-check mx-2 mt-1 align-content-center">
                                                        <label
                                                            className="form-label mb-0"
                                                            htmlFor="Yes"
                                                        >
                                                            Debit
                                                        </label>
                                                        <input
                                                            className="form-check-input"
                                                            id="Yes"
                                                            name="opening_balance_details.opening_balance_dr_cr"
                                                            checked={
                                                                formik.values
                                                                    .opening_balance_details
                                                                    ?.opening_balance_dr_cr ===
                                                                1
                                                            }
                                                            value={1}
                                                            onChange={() =>
                                                                formik.setFieldValue(
                                                                    "opening_balance_details.opening_balance_dr_cr",
                                                                    1
                                                                )
                                                            }
                                                            type="radio"
                                                        />
                                                    </div>
                                                    <div className="form-check mx-2 mt-1 align-content-center">
                                                        <label
                                                            className="form-label mb-0"
                                                            htmlFor="No"
                                                        >
                                                            Credit
                                                        </label>
                                                        <input
                                                            className="form-check-input"
                                                            id="No"
                                                            name="opening_balance_details.opening_balance_dr_cr"
                                                            checked={
                                                                formik.values
                                                                    .opening_balance_details
                                                                    ?.opening_balance_dr_cr ===
                                                                2
                                                            }
                                                            value={2}
                                                            onChange={() =>
                                                                formik.setFieldValue(
                                                                    "opening_balance_details.opening_balance_dr_cr",
                                                                    2
                                                                )
                                                            }
                                                            type="radio"
                                                        />
                                                    </div>
                                                </div>
                                            </Col>
                                        </Row>
                                        )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                )}
                            </Container>
                        </div>
                        {formik.values?.group_id ? (
                            <Container fluid className="p-0 mt-10 fixed-bottom-section">
                                <div className="d-flex flex-wrap gap-sm-4 gap-3 fixed-buttons px-lg-10 px-sm-8 px-6">
                                    <button
                                        type="submit"
                                        name="submitType"
                                        value="save"
                                        className="btn btn-primary"
                                        onClick={() => formik.setFieldValue("submitType", "save")}
                                        disabled={isDisable}
                                    >
                                        Save
                                    </button>
                                    {!id && (
                                        <button
                                            type="submit"
                                            name="submitType"
                                            value="saveAndNew"
                                            className="btn btn-primary"
                                            onClick={() =>
                                                formik.setFieldValue("submitType", "saveAndNew")
                                            }
                                            disabled={isDisable}
                                        >
                                            Save & New
                                        </button>
                                    )}
                                    <button
                                        type="button"
                                        className="btn btn-secondary"
                                        onClick={handleBack}
                                    >
                                        Back
                                    </button>
                                </div>
                            </Container>
                        ) : null}
                    </div>
                </form>
            )}
            <Modal show={swiftCodeShow} onHide={() => setSwiftCodeShow(false)} centered size='md' className={`modal fade custom_text_modal`}>
                <div className="modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header py-3">
                            <h5 className="modal-title">Change Label</h5>

                            <button
                                type="button"
                                className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                onClick={() => setSwiftCodeShow(false)}
                            >
                                <Close />
                            </button>
                        </div>
                        <div className="modal-body item-transaction">
                                <div className='d-flex flex-column gap-4 mt-2'>
                                <Form.Group className="position-relative form-floating-group">
                                    <FormInput
                                        className="floating-label-input"
                                        type="text"
                                        placeholder=""
                                        value={swiftCodeLabel}
                                        onChange={(e) => setSwiftCodeLabel(e.target.value)}
                                    />
                                    <Form.Label>Label Name</Form.Label>
                                </Form.Group>

                                <button
                                    type="button"
                                    className="btn btn-primary swift-code-btn mt-3"
                                    onClick={handleSwiftCode}
                                >
                                    Save
                                </button>
                                </div>
                        </div>

                    </div>
                </div>
            </Modal>
            {isItemCatModel && (
                <AddItemCategoryModal
                    show={isItemCatModel}
                    handleClose={closeItemCatModel}
                    isLedger={true}
                    itemFormik={formik}
                    setFields={setFields}
                    ledgerDetailOptions={ledgerDetailOptions}
                    isCall={true}
                    isAllLedger={isAllLedger}
                    groupLedgerList={groupLedgerList}
                />
            )}
            {isTransportModel && (
                <AddTransportModal
                    show={isTransportModel}
                    handleClose={closeTransportModel}
                    setModalType={setTransporterDetail}
                    modalType={transporterDetail.modelType}
                    transport={transport}
                    setTransporterDetail={setTransporterDetail}
                    partyFormik={formik}
                />
            )}
            {isBrokerModel && (
                <AddBrokerModal
                    show={isBrokerModel}
                    handleClose={closeBrokerModel}
                    modalType={false}
                    setBrokerDetail={setBrokerDetail}
                    partyFormik={formik}
                />
            )}
            {isShowItemStockModel && (
                <AddItemStockModal
                    show={isShowItemStockModel}
                    handleClose={closeStockModel}
                    data={editStockDetail}
                    multiDate={multiDate}
                />
            )}
            {isLocationOfAsset && (
                <LocationOfAsset
                    show={isLocationOfAsset}
                    handleClose={closeLocationOfAssetModel}
                    modalType={false}
                    setBrokerDetail={setBrokerDetail}
                    partyFormik={formik}
                />
            )}
            {showGstNotUnique && (
                <WarningModal
                    show={showGstNotUnique}
                    handleSubmit={() => setShowGstNotUnique(false)}
                    message={gstNotUniqueMessage}
                    showConfirmButton
                />
            )}
            {showHoldingRatio && (
                <WarningModal
                    show={showHoldingRatio}
                    handleClose={() => setShowHoldingRatio(false)}
                    handleSubmit={handleHoldingConfirmation} // Confirm holding
                    message={holdingratioMessage}
                    confirmText="Yes"
                    showConfirmButton
                    showCancelButton
                />
            )}

            {showProfitLossRatio && (
                <WarningModal
                    show={showProfitLossRatio}
                    handleClose={() => setShowProfitLossRatio(false)}
                    handleSubmit={handleProfitLossConfirmation} // Confirm profit/loss
                    message={holdingratioMessage}
                    confirmText="Yes"
                    showConfirmButton
                    showCancelButton
                />
            )}

            {isDeleteItemStock && (
                <WarningModal
                    show={isDeleteItemStock}
                    handleClose={() => setIsDeleteItemStock(false)}
                    handleSubmit={handleDeleteItemStock} // Confirm profit/loss
                    message={`Are you sure want to delete this !`}
                    confirmText="Yes"
                    showConfirmButton
                    showCancelButton
                />
            )}

            {isShippingFromModel && (
                <ShippingFromModal
                    show={isShippingFromModel}
                    handleClose={closeShippingFromModel}
                    localDispatchAddress={shippingAddress}
                    setLocalDispatchAddress={setShippingAddress}
                    selectedAddress={selectShippingAddress}
                    setSelectedAddress={setSelectShippingAddress}
                    isLedgeScreen={true}
                    gstQuote={gstQuote}
                    selectShippingAddress={selectShippingAddress}
                    setSelectShippingAddress={setSelectShippingAddress}
                    setSelectedShippingAddressId={setSelectedShippingAddressId}
                />
            )}
            {showBulkDeleteModel && (
                    <WarningModal
                        show={showBulkDeleteModel}
                        handleClose={() => setShowBulkDeleteModel(false)}
                        handleSubmit={() => handleConfirmBulkDelete()}
                        message={`Are you sure want to delete this Bill !`}
                        confirmText="Yes"
                        showConfirmButton
                        showCancelButton
                    />
                )}
            <Toast />
            {isAddCessRate && <CessRateModal isAddCessRate={isAddCessRate} handleClose={() => setIsAddCessRate(false)} cessRateId={cessRateId} setCessRateId={setCessRateId} formik={formik} />}
        </>
    );
};

export default LedgerTransaction;
