import React, { useState, useContext } from 'react';
import { StateContext } from '../context/StateContext';

const ShortcutTester = () => {
    const [testInput, setTestInput] = useState('');
    const [logs, setLogs] = useState([]);
    const { hasUnsavedChanges, isFieldsChanges, setHasUnsavedChanges, setisFieldsChanges } = useContext(StateContext);

    const addLog = (message) => {
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    };

    const simulateUnsavedChanges = () => {
        setHasUnsavedChanges(true);
        setisFieldsChanges(true);
        addLog('Simulated unsaved changes - hasUnsavedChanges: true, isFieldsChanges: true');
    };

    const clearUnsavedChanges = () => {
        setHasUnsavedChanges(false);
        setisFieldsChanges(false);
        addLog('Cleared unsaved changes - hasUnsavedChanges: false, isFieldsChanges: false');
    };

    const clearLogs = () => {
        setLogs([]);
    };

    return (
        <div style={{ 
            position: 'fixed', 
            top: '10px', 
            right: '10px', 
            width: '400px', 
            backgroundColor: 'white', 
            border: '2px solid #007bff', 
            borderRadius: '8px', 
            padding: '15px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            zIndex: 9999,
            fontSize: '12px'
        }}>
            <h4 style={{ margin: '0 0 15px 0', color: '#007bff' }}>Keyboard Shortcuts Tester</h4>
            
            <div style={{ marginBottom: '15px' }}>
                <strong>Current State:</strong>
                <div>hasUnsavedChanges: <span style={{ color: hasUnsavedChanges ? 'red' : 'green' }}>{hasUnsavedChanges.toString()}</span></div>
                <div>isFieldsChanges: <span style={{ color: isFieldsChanges ? 'red' : 'green' }}>{isFieldsChanges.toString()}</span></div>
            </div>

            <div style={{ marginBottom: '15px' }}>
                <button 
                    onClick={simulateUnsavedChanges}
                    style={{ 
                        padding: '5px 10px', 
                        marginRight: '5px', 
                        backgroundColor: '#dc3545', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        fontSize: '11px'
                    }}
                >
                    Simulate Unsaved Changes
                </button>
                <button 
                    onClick={clearUnsavedChanges}
                    style={{ 
                        padding: '5px 10px', 
                        backgroundColor: '#28a745', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        fontSize: '11px'
                    }}
                >
                    Clear Changes
                </button>
            </div>

            <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    Test Input (for Ctrl+A and Escape):
                </label>
                <input
                    type="text"
                    value={testInput}
                    onChange={(e) => setTestInput(e.target.value)}
                    placeholder="Type here and test shortcuts..."
                    style={{ 
                        width: '100%', 
                        padding: '8px', 
                        border: '1px solid #ccc', 
                        borderRadius: '4px',
                        fontSize: '12px'
                    }}
                />
            </div>

            <div style={{ marginBottom: '15px' }}>
                <strong>Shortcuts to Test:</strong>
                <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                    <li><strong>F8:</strong> Navigate to Dashboard</li>
                    <li><strong>Escape:</strong> Remove focus OR go back</li>
                    <li><strong>Ctrl+A:</strong> Select all in input OR open Sale</li>
                </ul>
            </div>

            <div style={{ marginBottom: '10px' }}>
                <strong>Activity Log:</strong>
                <button 
                    onClick={clearLogs}
                    style={{ 
                        float: 'right', 
                        padding: '2px 8px', 
                        backgroundColor: '#6c757d', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '3px',
                        fontSize: '10px'
                    }}
                >
                    Clear
                </button>
            </div>
            
            <div style={{ 
                maxHeight: '150px', 
                overflowY: 'auto', 
                backgroundColor: '#f8f9fa', 
                padding: '8px', 
                borderRadius: '4px',
                fontSize: '10px',
                fontFamily: 'monospace'
            }}>
                {logs.length === 0 ? (
                    <div style={{ color: '#6c757d' }}>No activity yet...</div>
                ) : (
                    logs.map((log, index) => (
                        <div key={index} style={{ marginBottom: '2px' }}>{log}</div>
                    ))
                )}
            </div>
        </div>
    );
};

export default ShortcutTester;
